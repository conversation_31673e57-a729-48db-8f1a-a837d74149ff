"""
TrendInsight API 路由

提供 TrendInsight 平台相关的 API 接口
"""

from typing import Dict, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException, Path, Query

from controllers.trendinsight.author_sync_controller import author_sync_controller
from controllers.trendinsight.keyword_sync_controller import keyword_sync_controller
from controllers.trendinsight.main_controller import trendinsight_main_controller
from controllers.trendinsight.video_process_controller import video_process_controller
from rpc.trendinsight.schemas import (
    AuthorDetailResponse,
    DarenSearchRequest,
    DarenSearchResponse,
    UserInfoResponse,
    VideoIndexResponse,
    VideoSearchResponse,
)
from schemas.responses import STANDARD_RESPONSES, ErrorResponse
from schemas.trendinsight import (
    AuthorSyncRequest,
    AuthorSyncResponse,
    KeywordSyncRequest,
    KeywordSyncResponse,
    VideoTrendSyncResponse,
)

router = APIRouter(
    prefix="/trendinsight",
    tags=["TrendInsight API"],
)


@router.get(
    "/user/self-info",
    response_model=UserInfoResponse,
    summary="查询用户自己的信息",
    description="""
获取当前登录用户的基本信息

**注意事项：**
- 系统会自动从数据库获取有效的 TrendInsight cookies
- 返回的信息包括用户ID、用户名、头像等基本资料

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/user/self-info"
```
    """,
    operation_id="get_user_self_info",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "成功获取用户信息",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "message": "success",
                        "data": {
                            "user_id": "123456789",
                            "user_name": "测试用户",
                            "avatar": "https://example.com/avatar.jpg",
                        },
                    }
                }
            },
        },
        404: {
            "model": ErrorResponse,
            "description": "未找到有效的cookies或用户信息",
            "content": {
                "application/json": {
                    "example": {"code": 404, "message": "Not Found", "detail": "未找到有效的trendinsight平台cookies"}
                }
            },
        },
    },
)
async def get_user_self_info() -> UserInfoResponse:
    """查询用户自己的信息"""
    return await trendinsight_main_controller.query_user_self_info()


@router.get(
    "/daren/search",
    response_model=DarenSearchResponse,
    summary="关键词搜索达人",
    description="""
根据搜索条件查询达人/创作者的信息

**功能说明：**
- 支持按关键词搜索达人
- 可以获取达人的基本信息、粉丝数、作品数等
- 支持分页查询
- 系统会自动从数据库获取有效的 TrendInsight cookies

**查询参数：**
- keyword: 搜索关键词（必填）
- total: 返回结果总数（可选，默认30）

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/daren/search?keyword=科技达人&total=10"
```
    """,
    operation_id="search_daren_users",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "成功查询达人信息",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "message": "success",
                        "data": {
                            "has_more": True,
                            "cursor": 10,
                            "list": [
                                {
                                    "user_id": "123456",
                                    "user_name": "科技达人",
                                    "fans_count": "100万",
                                    "item_count": "500",
                                }
                            ],
                        },
                    }
                }
            },
        },
    },
)
async def search_daren_users(
    keyword: str = Query(..., description="搜索关键词", example="科技达人", title="搜索关键词"),
    total: int = Query(30, description="返回结果总数", example=10, title="返回结果总数", ge=1, le=100),
) -> DarenSearchResponse:
    """查询达人信息"""
    # 创建请求对象
    request = DarenSearchRequest(keyword=keyword, total=total)
    return await trendinsight_main_controller.query_daren_sug_great_user_list(request)


@router.get(
    "/video/search",
    response_model=VideoSearchResponse,
    summary="关键词搜索视频",
    description="""
根据关键词搜索相关视频内容

**功能说明：**
- 支持按关键词搜索视频
- 可以筛选特定作者的视频
- 支持按分类、日期、时长等条件筛选
- 返回视频的基本信息、统计数据等
- 系统会自动从数据库获取有效的 TrendInsight cookies

**查询参数：**
- keyword: 搜索关键词（必填）
- author_ids: 作者ID列表，用逗号分隔（可选）
- category_id: 分类ID（可选，默认"0"）
- date_type: 日期类型（可选，默认0）
- label_type: 标签类型（可选，默认0）
- duration_type: 时长类型（可选，默认0）

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/video/search?keyword=科技&category_id=2"
```
    """,
    operation_id="search_videos_by_keyword",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "成功搜索视频",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "message": "success",
                        "data": {
                            "has_more": True,
                            "cursor": 20,
                            "videos": [
                                {
                                    "video_id": "7123456789",
                                    "title": "科技前沿解析",
                                    "author_name": "科技博主",
                                    "play_count": 100000,
                                    "like_count": 5000,
                                }
                            ],
                        },
                    }
                }
            },
        },
    },
)
async def search_videos_by_keyword(
    keyword: str = Query(..., description="搜索关键词", example="科技", title="搜索关键词"),
    author_ids: Optional[str] = Query(
        None,
        description="作者ID列表，多个ID用逗号分隔",
        example="123456,789012",
        title="作者ID列表",
        pattern=r"^[0-9]+(,[0-9]+)*$",
    ),
    category_id: str = Query("0", description="分类ID", example="0", title="分类ID", pattern=r"^\d+$"),
    date_type: int = Query(0, description="日期类型", example=0, title="日期类型", ge=0, le=10),
    label_type: int = Query(0, description="标签类型", example=0, title="标签类型", ge=0, le=10),
    duration_type: int = Query(0, description="时长类型", example=0, title="时长类型", ge=0, le=10),
) -> VideoSearchResponse:
    """关键词搜索视频"""
    # 处理 author_ids 参数
    author_ids_list = author_ids.split(",") if author_ids else None

    return await trendinsight_main_controller.search_info_by_keyword(
        keyword=keyword,
        author_ids=author_ids_list,
        category_id=category_id,
        date_type=date_type,
        label_type=label_type,
        duration_type=duration_type,
    )


@router.get(
    "/author/{user_id}",
    response_model=AuthorDetailResponse,
    summary="获取作者详情",
    description="""
根据用户ID获取作者的详细信息

**功能说明：**
- 获取指定作者的详细信息
- 包括基本资料、统计数据、作品信息等
- 支持获取作者的粉丝数、作品数、获赞数等关键指标
- 系统会自动从数据库获取有效的 TrendInsight cookies

**路径参数：**
- user_id: 用户ID（必填）

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/author/123456789"
```
    """,
    operation_id="get_author_detail",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "成功获取作者详情",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "message": "success",
                        "data": {
                            "user_id": "123456789",
                            "user_name": "科技博主",
                            "user_head_logo": "https://example.com/avatar.jpg",
                            "fans_count": "100万",
                            "item_count": "500",
                            "like_count": "1000万",
                        },
                    }
                }
            },
        },
        404: {
            "model": ErrorResponse,
            "description": "作者不存在",
            "content": {
                "application/json": {"example": {"code": 404, "message": "Not Found", "detail": "指定的作者不存在"}}
            },
        },
    },
)
async def get_author_detail(
    user_id: str = Path(..., description="用户ID", example="123456789", title="用户ID"),
) -> AuthorDetailResponse:
    """获取作者详情"""
    return await trendinsight_main_controller.get_author_detail(user_id)


@router.get(
    "/video/{item_id}/index",
    response_model=VideoIndexResponse,
    summary="获取视频指数数据",
    description="""
获取指定视频在特定时间范围内的指数数据

**功能说明：**
- 获取视频的热度指数、传播指数等数据
- 支持指定时间范围查询
- 提供视频表现的量化分析
- 系统会自动从数据库获取有效的 TrendInsight cookies

**路径参数：**
- item_id: 视频ID（必填）

**查询参数：**
- start_date: 开始日期，格式YYYYMMDD（必填）
- end_date: 结束日期，格式YYYYMMDD（必填）

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/video/7123456789/index?start_date=20250101&end_date=20250107"
```
    """,
    operation_id="get_video_index",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "成功获取视频指数数据",
            "content": {
                "application/json": {
                    "example": {
                        "status_code": 0,
                        "message": "success",
                        "data": {
                            "video_id": "7123456789",
                            "heat_index": 85.6,
                            "spread_index": 92.3,
                            "engagement_index": 78.9,
                            "date_range": "2025-01-01 to 2025-01-07",
                        },
                    }
                }
            },
        },
        400: {
            "model": ErrorResponse,
            "description": "日期格式错误",
            "content": {
                "application/json": {
                    "example": {"code": 400, "message": "Bad Request", "detail": "日期格式错误，请使用YYYYMMDD格式"}
                }
            },
        },
    },
)
async def get_video_index(
    item_id: str = Path(..., description="视频ID", example="7123456789012345678", title="视频ID"),
    start_date: str = Query(
        ..., description="开始日期，格式：YYYYMMDD", example="20250101", title="开始日期", pattern=r"^\d{8}$"
    ),
    end_date: str = Query(
        ..., description="结束日期，格式：YYYYMMDD", example="20250107", title="结束日期", pattern=r"^\d{8}$"
    ),
) -> VideoIndexResponse:
    """获取视频指数数据"""
    return await trendinsight_main_controller.get_video_index(item_id, start_date, end_date)


@router.post(
    "/video/{aweme_id}/sync-trend",
    response_model=VideoTrendSyncResponse,
    summary="同步视频趋势数据",
    description="""
同步指定视频的趋势指数数据到数据库

**功能说明：**
- 调用 TrendInsight API 获取视频的趋势指数数据
- 提取 trend 数组中最后一项的值作为趋势评分
- 将趋势评分与视频ID同步到 trendinsight_video 表
- 自动查询最近7天的趋势数据

**处理流程：**
1. 接收抖音视频ID参数
2. 调用 TrendInsight API 获取视频指数数据
3. 从 trend 数组中提取最后一项的值作为趋势评分
4. 将趋势评分存储到 trendinsight_video 表
5. 返回同步结果和视频趋势数据

**参数说明：**
- `aweme_id`: 抖音视频ID，必须是有效的视频标识符

**返回结果：**
- `success`: 布尔值，表示操作是否成功
- `message`: 字符串，包含操作结果的详细信息
- `data`: 对象，成功时包含视频趋势数据（aweme_id、trend_score、时间戳等）
- `error`: 字符串，失败时包含错误信息

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/trendinsight/video/7123456789012345678/sync-trend"
```
    """,
    operation_id="sync_video_trend_score",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "同步完成（无论成功或失败都返回200状态码）",
            "model": VideoTrendSyncResponse,
        },
    },
)
async def sync_video_trend_score(
    aweme_id: str = Path(..., description="抖音视频ID", example="7123456789012345678", title="视频ID"),
) -> VideoTrendSyncResponse:
    """同步视频趋势数据"""
    try:
        from models.trendinsight.models import (
            TrendInsightVideo,
            fetch_and_store_video_trend_score,
        )

        # 调用函数获取并存储趋势数据
        success = await fetch_and_store_video_trend_score(aweme_id)

        if success:
            # 查询存储的数据
            try:
                video = await TrendInsightVideo.get(id=aweme_id)
                return {
                    "success": True,
                    "message": "成功同步趋势数据",
                    "data": {
                        "aweme_id": video.aweme_id,
                        "trend_score": video.trend_score,
                        "created_at": video.created_at.isoformat(),
                        "updated_at": video.updated_at.isoformat(),
                        "is_deleted": video.is_deleted,
                    },
                }
            except TrendInsightVideo.DoesNotExist:
                return {"success": False, "message": "数据同步成功但查询失败", "error": "视频记录未找到"}
        else:
            return {
                "success": False,
                "message": "同步趋势数据失败",
                "error": "可能的原因：cookies无效、视频不存在、网络问题或API限流",
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"同步视频趋势数据失败: {str(e)}")


@router.get(
    "/ping",
    summary="测试接口连通性",
    description="""
测试 TrendInsight API 接口是否可用

**功能说明：**
- 验证数据库中存储的 cookies 的有效性
- 测试与TrendInsight平台的连接状态
- 用于健康检查和故障诊断
- 系统会自动从数据库获取有效的 TrendInsight cookies

**使用示例：**
```bash
curl -X GET "http://localhost:8000/api/v1/trendinsight/ping"
```
    """,
    operation_id="ping_trendinsight",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {"description": "接口连通性测试成功", "content": {"application/json": {"example": {"success": True}}}},
        500: {
            "model": ErrorResponse,
            "description": "接口连接失败",
            "content": {
                "application/json": {
                    "example": {"code": 500, "message": "Internal Server Error", "detail": "TrendInsight接口连接失败"}
                }
            },
        },
    },
)
async def ping_trendinsight() -> Dict[str, bool]:
    """测试接口连通性"""
    result = await trendinsight_main_controller.pong()
    return {"success": result}


@router.post(
    "/author/sync",
    response_model=AuthorSyncResponse,
    summary="同步作者和视频数据",
    description="""
同步 TrendInsight 作者和其抖音视频数据

**功能说明：**
- 接收 TrendInsight 用户ID，查询或创建作者记录
- 提取作者的抖音用户ID，通过抖音RPC服务获取视频列表
- 在 trendinsight_video_related 表中创建视频关联记录（不存在才创建）

**业务流程：**
1. 检查 trendinsight_author 表中是否存在该用户
2. 不存在则调用 TrendInsight API 获取作者详情并创建记录
3. 提取作者的抖音用户ID（从 user_aweme_url 中解析）
4. 调用抖音RPC服务获取该用户的视频列表
5. 为每个视频在 trendinsight_video_related 表中创建关联记录

**注意事项：**
- 系统会自动从数据库获取有效的 TrendInsight cookies
- 需要数据库中有有效的抖音平台 cookies
- 关联记录不会重复创建
- 支持增量同步，已存在的数据不会重复处理

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/trendinsight/author/sync" \\
     -H "Content-Type: application/json" \\
     -d '{"user_id": "heicgcbajggjdjjaefj"}'
```
    """,
    operation_id="sync_author_videos",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "同步成功",
            "content": {
                "application/json": {
                    "example": {
                        "author_action": "created",
                        "author_data": {
                            "user_id": "heicgcbajggjdjjaefj",
                            "user_name": "科技博主",
                            "douyin_user_id": "MS4wLjABAAAA123",
                        },
                        "videos_synced": 15,
                        "videos_failed": 0,
                        "relations_created": 15,
                        "relations_existing": 0,
                        "aweme_ids": ["7123456789012345678", "7123456789012345679", "7123456789012345680"],
                        "errors": [],
                    }
                }
            },
        },
        400: {
            "model": ErrorResponse,
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {"code": 400, "message": "Bad Request", "detail": "user_id 参数不能为空"}
                }
            },
        },
        404: {
            "model": ErrorResponse,
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "example": {
                        "code": 404,
                        "message": "Not Found",
                        "detail": "未找到有效的TrendInsight平台cookies或用户不存在",
                    }
                }
            },
        },
        500: {
            "model": ErrorResponse,
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "message": "Internal Server Error",
                        "detail": "同步作者和视频失败: 网络连接超时",
                    }
                }
            },
        },
    },
)
async def sync_author_videos(
    request: AuthorSyncRequest,
    background_tasks: BackgroundTasks,
) -> AuthorSyncResponse:
    """同步作者和视频数据"""
    sync_result = await author_sync_controller.sync_author_videos(request.user_id)

    # 启动后台任务获取缺失的视频详情数据
    if sync_result.aweme_ids:
        background_tasks.add_task(video_process_controller.fetch_video_trend_scores, sync_result.aweme_ids)

    return sync_result


@router.post(
    "/keyword/sync",
    response_model=KeywordSyncResponse,
    summary="同步关键词和视频数据",
    description="""
同步指定关键词相关的视频数据

**功能说明：**
- 接收搜索关键词，计算哈希值检查或创建关键词记录
- 通过 TrendInsight API 搜索该关键词相关的视频
- 在 trendinsight_video_related 表中创建视频关联记录（不存在才创建）
- 启动后台任务处理视频的指数数据（index 字段）

**业务流程：**
1. 计算关键词的 MD5 哈希值
2. 检查 trendinsight_keyword 表中是否存在该关键词记录
3. 不存在则创建新的关键词记录
4. 调用 TrendInsight API 搜索该关键词相关的视频
5. 为每个搜索到的视频在 trendinsight_video_related 表中创建关联记录
6. 启动后台任务处理视频的指数数据，直接使用返回数据中的 index 字段

**注意事项：**
- 系统会自动从数据库获取有效的 TrendInsight cookies
- 使用批量操作提高数据库性能
- 关联记录不会重复创建
- 支持增量同步，已存在的数据不会重复处理
- 后台任务会处理视频的指数信息，无需额外计算

**使用示例：**
```bash
curl -X POST "http://localhost:8000/api/v1/trendinsight/keyword/sync" \\
     -H "Content-Type: application/json" \\
     -d '{"keyword": "科技前沿"}'
```
    """,
    operation_id="sync_keyword_videos",
    tags=["TrendInsight API"],
    responses={
        **STANDARD_RESPONSES,
        200: {
            "description": "同步成功",
            "content": {
                "application/json": {
                    "example": {
                        "keyword_action": "created",
                        "keyword_data": {
                            "keyword": "科技前沿",
                            "keyword_hash": "abc123def456",
                            "video_count": 0,
                        },
                        "videos_synced": 25,
                        "videos_failed": 0,
                        "relations_created": 20,
                        "relations_existing": 5,
                        "aweme_ids": ["7123456789012345678", "7123456789012345679", "7123456789012345680"],
                        "errors": [],
                    }
                }
            },
        },
        400: {
            "model": ErrorResponse,
            "description": "请求参数错误",
            "content": {
                "application/json": {
                    "example": {"code": 400, "message": "Bad Request", "detail": "keyword 参数不能为空"}
                }
            },
        },
        404: {
            "model": ErrorResponse,
            "description": "资源不存在",
            "content": {
                "application/json": {
                    "example": {"code": 404, "message": "Not Found", "detail": "未找到有效的TrendInsight平台cookies"}
                }
            },
        },
        500: {
            "model": ErrorResponse,
            "description": "服务器内部错误",
            "content": {
                "application/json": {
                    "example": {
                        "code": 500,
                        "message": "Internal Server Error",
                        "detail": "同步关键词和视频失败: 网络连接超时",
                    }
                }
            },
        },
    },
)
async def sync_keyword_videos(
    request: KeywordSyncRequest,
    background_tasks: BackgroundTasks,
) -> KeywordSyncResponse:
    """同步关键词和视频数据

    同步指定关键词相关的视频列表，并启动后台任务获取视频的详细信息和趋势数据。

    Args:
        request: 关键词同步请求对象，包含要同步的关键词
        background_tasks: FastAPI 后台任务管理器

    Returns:
        KeywordSyncResponse: 关键词同步结果，包含关键词信息、视频列表和同步统计

    Example:
        {
            "keyword_action": "created",
            "keyword_data": {
                "id": 123,
                "keyword": "科技前沿",
                "keyword_hash": "abc123def456",
                "video_count": 25,
                "created_at": "2025-01-01T00:00:00",
                "updated_at": "2025-01-01T00:00:00"
            },
            "videos_synced": 25,
            "videos_failed": 0,
            "relations_created": 20,
            "relations_existing": 5,
            "video_items": [
                {
                    "aweme_id": "7123456789012345678",
                    "aweme_type": "video",
                    "title": "科技前沿视频",
                    "desc": "这是一个关于科技前沿的视频",
                    "create_time": 1705314645,
                    "user_id": "123456789",
                    "nickname": "科技博主",
                    "liked_count": "1000",
                    "comment_count": "50",
                    "share_count": "20",
                    "collected_count": "30",
                    "source_keyword": "科技前沿"
                }
            ],
            "errors": []
        }
    """
    sync_result = await keyword_sync_controller.sync_keyword_videos(request.keyword)

    # 启动后台任务处理视频的指数数据
    if sync_result.video_items:
        # 提取视频ID和对应的index值
        video_index_data = []
        for video_item in sync_result.video_items:
            # 从转换后的视频数据中提取index值（如果存在）
            if video_item.index:
                video_index_data.append({"aweme_id": video_item.aweme_id, "index": video_item.index})

        # 如果有index数据，启动后台任务处理
        if video_index_data:
            background_tasks.add_task(video_process_controller.process_keyword_video_index_data, video_index_data)

    return sync_result
