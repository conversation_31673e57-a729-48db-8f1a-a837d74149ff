"""
抖音相关控制器

提供抖音平台数据的业务逻辑处理
"""

from typing import Any, Dict, Optional, Union

from fastapi import HTTPException

from core.crud import CRUDBase
from models.douyin.models import DouyinAweme
from models.enums import Platform
from rpc.douyin import async_douyin_api
from rpc.douyin.schemas import (
    DiscoverSearchRequest,
    DiscoverSearchResponse,
    VideoDetailRequest,
    VideoDetailResponse,
)
from schemas.douyin import DouyinAwemeCreate, DouyinAwemeUpdate

from .collection_controller import DouyinCollectionController

# Removed unused imports from douyin_data_extractor as they are not used in this file
from .video_controller import DouyinVideoController


class DouyinController(CRUDBase[DouyinAweme, DouyinAwemeCreate, DouyinAwemeUpdate]):
    """抖音控制器"""

    def __init__(self):
        super().__init__(model=DouyinAweme)
        self.collection_controller = DouyinCollectionController()

    async def _get_douyin_client(self):
        """获取抖音客户端实例"""

        return async_douyin_api

    def _convert_rpc_to_db_model(self, rpc_response: VideoDetailResponse, by_mobile: bool = False) -> Dict:
        """
        将RPC响应转换为数据库模型数据格式

        Args:
            rpc_response: RPC响应对象
            by_mobile: 是否为移动端数据，默认为False

        Returns:
            Dict: 符合DouyinAweme模型字段要求的数据字典
        """
        if not rpc_response.aweme_detail:
            raise HTTPException(status_code=404, detail="视频详情为空")

        detail = rpc_response.aweme_detail

        # 导入必要的函数
        from models.douyin.models import safe_convert_to_datetime

        # 提取作者信息和统计信息
        author = detail.author
        statistics = detail.statistics

        # 构建用户信息字典（用于兼容原有的字典格式处理函数）
        user_info = {}
        if author:
            user_info = {
                "uid": getattr(author, "uid", ""),
                "sec_uid": getattr(author, "sec_uid", ""),
                "short_id": getattr(author, "short_id", ""),
                "unique_id": getattr(author, "unique_id", ""),
                "nickname": getattr(author, "nickname", ""),
                "signature": getattr(author, "signature", ""),
                "avatar_thumb": getattr(author, "avatar_thumb", {}),
                "avatar_medium": getattr(author, "avatar_medium", {}),
                "avatar_larger": getattr(author, "avatar_larger", {}),
            }

        # 构建统计信息字典
        interact_info = {}
        if statistics:
            interact_info = {
                "digg_count": getattr(statistics, "digg_count", 0),
                "comment_count": getattr(statistics, "comment_count", 0),
                "share_count": getattr(statistics, "share_count", 0),
                "collect_count": getattr(statistics, "collect_count", 0),
            }

        # 构建视频信息字典（处理video对象或字典）
        video_info = {}
        if hasattr(detail, "video") and detail.video:
            video_obj = detail.video

            # 如果video已经是字典，直接使用
            if isinstance(video_obj, dict):
                video_info = video_obj
            else:
                # 如果video是对象，转换为字典
                # 处理封面信息
                cover_info = {}
                if hasattr(video_obj, "cover") and video_obj.cover:
                    cover_obj = video_obj.cover
                    if isinstance(cover_obj, dict):
                        cover_info = cover_obj
                    else:
                        cover_info = {"url_list": getattr(cover_obj, "url_list", [])}

                # 处理播放地址信息
                def extract_play_addr(play_addr_obj):
                    if not play_addr_obj:
                        return {}
                    if isinstance(play_addr_obj, dict):
                        return play_addr_obj
                    return {"url_list": getattr(play_addr_obj, "url_list", [])}

                video_info = {
                    "cover": cover_info,
                    "play_addr_h264": extract_play_addr(getattr(video_obj, "play_addr_h264", None)),
                    "play_addr_256": extract_play_addr(getattr(video_obj, "play_addr_256", None)),
                    "play_addr": extract_play_addr(getattr(video_obj, "play_addr", None)),
                }

        # 构建视频数据字典（用于兼容原有的字典格式处理函数）
        aweme_item = {
            "aweme_id": str(detail.aweme_id),
            "aweme_type": getattr(detail, "aweme_type", 0),
            "desc": getattr(detail, "desc", ""),
            "create_time": getattr(detail, "create_time", 0),
            "share_url": getattr(detail, "share_url", ""),
            "author": user_info,
            "statistics": interact_info,
            "video": video_info,
            "ip_label": getattr(detail, "ip_label", ""),
            "source_keyword": "",  # 这个字段通常由调用方设置
        }

        # 使用与update_douyin_aweme相同的字段映射逻辑
        aweme_data = {
            # 基本信息
            "aweme_id": str(aweme_item.get("aweme_id", "")),
            "aweme_type": str(aweme_item.get("aweme_type", "0")),
            "title": aweme_item.get("desc", "")[:1024],  # 限制标题长度
            "desc": aweme_item.get("desc", ""),
            "create_time": safe_convert_to_datetime(aweme_item.get("create_time")),
            # 用户信息（从author字段提取）
            "user_id": user_info.get("uid", user_info.get("short_id", "")),  # 兼容不同字段名
            "sec_uid": user_info.get("sec_uid", ""),
            "short_user_id": user_info.get("short_id", ""),
            "user_unique_id": user_info.get("unique_id", ""),
            "nickname": user_info.get("nickname", ""),
            "user_signature": user_info.get("signature", ""),
            "avatar": self._extract_avatar_url_from_dict(user_info),
            # 统计信息（从statistics字段提取）
            "liked_count": str(interact_info.get("digg_count", 0)),
            "comment_count": str(interact_info.get("comment_count", 0)),
            "share_count": str(interact_info.get("share_count", 0)),
            "collected_count": str(interact_info.get("collect_count", 0)),
            # 位置信息
            "ip_location": aweme_item.get("ip_label", ""),
            # URL信息
            "aweme_url": self._extract_aweme_detail_url(aweme_item),
            "cover_url": self._extract_content_cover_url(aweme_item),
            "video_download_url": self._extract_video_download_url_from_dict(aweme_item, by_mobile),
            # 其他信息
            "source_keyword": aweme_item.get("source_keyword", ""),
        }

        return aweme_data

    def _convert_html_aweme_to_db_model(self, aweme_item: dict, by_mobile: bool = False) -> Dict[str, Any]:
        """
        将HTML提取的AwemeItem数据转换为数据库模型数据格式

        Args:
            aweme_item: HTML提取的AwemeItem数据字典
            by_mobile: 是否为移动端数据，默认为False

        Returns:
            Dict: 符合DouyinAweme模型字段要求的数据字典
        """
        # 导入必要的函数
        from models.douyin.models import safe_convert_to_datetime

        # 提取嵌套的用户信息和统计信息
        user_info = aweme_item.get("author", {})
        interact_info = aweme_item.get("statistics", {})

        # 使用与原update_douyin_aweme相同的字段映射逻辑
        aweme_data = {
            # 基本信息
            "aweme_id": str(aweme_item.get("aweme_id", "")),
            "aweme_type": str(aweme_item.get("aweme_type", "0")),
            "title": aweme_item.get("desc", "")[:1024],  # 限制标题长度
            "desc": aweme_item.get("desc", ""),
            "create_time": safe_convert_to_datetime(aweme_item.get("create_time")),
            # 用户信息（从author字段提取）
            "user_id": user_info.get("uid", user_info.get("short_id", "")),  # 兼容不同字段名
            "sec_uid": user_info.get("sec_uid", ""),
            "short_user_id": user_info.get("short_id", ""),
            "user_unique_id": user_info.get("unique_id", ""),
            "nickname": user_info.get("nickname", ""),
            "user_signature": user_info.get("signature", ""),
            "avatar": self._extract_avatar_url_from_dict(user_info),
            # 统计信息（从statistics字段提取）
            "liked_count": str(interact_info.get("digg_count", 0)),
            "comment_count": str(interact_info.get("comment_count", 0)),
            "share_count": str(interact_info.get("share_count", 0)),
            "collected_count": str(interact_info.get("collect_count", 0)),
            # 位置信息
            "ip_location": aweme_item.get("ip_label", ""),
            # URL信息
            "aweme_url": self._extract_aweme_detail_url(aweme_item),
            "cover_url": self._extract_content_cover_url(aweme_item),
            "video_download_url": self._extract_video_download_url_from_dict(aweme_item, by_mobile),
            # 其他信息
            "source_keyword": aweme_item.get("source_keyword", ""),
        }

        return aweme_data

    def _extract_avatar_url_from_dict(self, user_info: dict) -> str:
        """
        从用户信息字典中提取头像URL

        Args:
            user_info (dict): 用户信息字典

        Returns:
            str: 头像URL
        """
        if not user_info:
            return ""

        # 尝试从avatar_thumb获取
        avatar_thumb = user_info.get("avatar_thumb", {})
        if isinstance(avatar_thumb, dict):
            url_list = avatar_thumb.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

        # 尝试从avatar_medium获取
        avatar_medium = user_info.get("avatar_medium", {})
        if isinstance(avatar_medium, dict):
            url_list = avatar_medium.get("url_list", [])
            if url_list and len(url_list) > 0:
                return str(url_list[0])

        return ""

    def _extract_aweme_detail_url(self, aweme_item: dict) -> str:
        """
        提取视频详情页URL

        Args:
            aweme_item (dict): 视频数据字典

        Returns:
            str: 视频详情页URL
        """
        if not aweme_item:
            return ""

        aweme_id = aweme_item.get("aweme_id")
        if aweme_id:
            return f"https://www.douyin.com/video/{aweme_id}"

        return ""

    def _extract_content_cover_url(self, aweme_item: dict) -> str:
        """
        提取视频封面URL

        Args:
            aweme_item (dict): 视频数据字典

        Returns:
            str: 视频封面URL
        """
        if not aweme_item:
            return ""

        # 从video字段提取封面
        video_info = aweme_item.get("video", {})
        if isinstance(video_info, dict):
            cover_info = video_info.get("cover", {})
            if isinstance(cover_info, dict):
                url_list = cover_info.get("url_list", [])
                if url_list and len(url_list) > 0:
                    return str(url_list[0])

        return ""

    def _extract_video_download_url_from_dict(self, aweme_detail: dict, by_mobile: bool = False) -> str:
        """
        从视频数据字典中提取视频下载地址

        Args:
            aweme_detail (dict): 抖音视频详情数据
            by_mobile (bool): 是否为移动端数据，默认为False

        Returns:
            str: 视频下载地址
        """
        # 参数验证：检查 aweme_detail 是否为 None 或空值
        if not aweme_detail:
            return ""

        video_item = aweme_detail.get("video", {})
        url_h264_list = video_item.get("play_addr_h264", {}).get("url_list", [])
        url_256_list = video_item.get("play_addr_256", {}).get("url_list", [])
        url_list = video_item.get("play_addr", {}).get("url_list", [])
        actual_url_list = url_h264_list or url_256_list or url_list

        if not actual_url_list or (not by_mobile and len(actual_url_list) < 2):
            return ""

        return actual_url_list[-1]

    def _extract_avatar_url(self, author) -> str:
        """提取用户头像URL"""
        if not author:
            return ""

        # 尝试多个可能的头像字段
        avatar_fields = ["avatar_thumb", "avatar_medium", "avatar_larger"]
        for field in avatar_fields:
            if hasattr(author, field):
                avatar_data = getattr(author, field)
                if isinstance(avatar_data, dict) and "url_list" in avatar_data:
                    url_list = avatar_data["url_list"]
                    if url_list and len(url_list) > 0:
                        return str(url_list[0])
                elif isinstance(avatar_data, str):
                    return str(avatar_data)

        return ""

    def _extract_cover_url(self, detail) -> str:
        """提取视频封面URL"""
        if not detail:
            return ""

        # 尝试多个可能的封面字段
        if hasattr(detail, "video") and detail.video:
            video = detail.video
            cover_fields = ["cover", "origin_cover", "dynamic_cover"]
            for field in cover_fields:
                if hasattr(video, field):
                    cover_data = getattr(video, field)
                    if isinstance(cover_data, dict) and "url_list" in cover_data:
                        url_list = cover_data["url_list"]
                        if url_list and len(url_list) > 0:
                            return str(url_list[0])

        return ""

    def _extract_video_download_url(self, detail) -> str:
        """提取视频下载URL"""
        if not detail:
            return ""

        # 尝试多个可能的视频URL字段
        if hasattr(detail, "video") and detail.video:
            video = detail.video
            video_fields = ["play_addr", "download_addr", "bit_rate"]
            for field in video_fields:
                if hasattr(video, field):
                    video_data = getattr(video, field)
                    if isinstance(video_data, dict) and "url_list" in video_data:
                        url_list = video_data["url_list"]
                        if url_list and len(url_list) > 0:
                            return str(url_list[0])
                    elif isinstance(video_data, list) and len(video_data) > 0:
                        # bit_rate 通常是数组格式
                        first_rate = video_data[0]
                        if isinstance(first_rate, dict) and "play_addr" in first_rate:
                            play_addr = first_rate["play_addr"]
                            if isinstance(play_addr, dict) and "url_list" in play_addr:
                                url_list = play_addr["url_list"]
                                if url_list and len(url_list) > 0:
                                    return str(url_list[0])

        return ""

    # 方法1: RPC + 传入cookies，返回RPC类型
    async def get_video_rpc_with_cookies(self, video_id: str, cookies: str) -> VideoDetailResponse:
        """
        传入cookies获取视频详情，返回RPC原响应格式

        Args:
            video_id: 视频ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            VideoDetailResponse: RPC原响应格式
        """
        try:
            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            response = await client.get_video_detail(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 方法2: RPC + 自动获取cookies，返回RPC类型
    async def get_video_rpc_auto_cookies(self, video_id: str) -> VideoDetailResponse:
        """
        自动获取cookies并获取视频详情，返回RPC原响应格式

        Args:
            video_id: 视频ID

        Returns:
            VideoDetailResponse: RPC原响应格式
        """
        try:
            # 从数据库获取cookies
            cookies = await self._get_cookies_from_db(Platform.DOUYIN)

            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            response = await client.get_video_detail(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    async def get_video_detail_rpc(self, aweme_id: str) -> VideoDetailResponse:
        """
        使用 RPC API 获取视频详情，cookies 由账号提供者自动管理

        Args:
            aweme_id: 视频ID

        Returns:
            VideoDetailResponse: RPC响应格式
        """
        try:
            from rpc.douyin import async_douyin_api
            from rpc.douyin.schemas import VideoDetailRequest

            request = VideoDetailRequest(aweme_id=aweme_id)
            response = await async_douyin_api.get_video_detail(request)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 方法3: 数据库 + 传入cookies，返回dict
    async def get_video_db_with_cookies(self, video_id: str, cookies: str) -> Dict:
        """
        传入cookies获取视频详情，返回数据库模型格式

        Args:
            video_id: 视频ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            Dict: 数据库模型字典格式
        """
        try:
            # 先从数据库查询
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()

            # 数据库没有则调用RPC获取并保存
            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            rpc_response = await client.get_video_detail(request, cookies)

            # 转换数据格式
            aweme_data = self._convert_rpc_to_db_model(rpc_response)

            # 使用参考文件的方式保存到数据库
            from models.douyin.models import update_douyin_aweme

            success = await update_douyin_aweme(aweme_data)

            if not success:
                raise HTTPException(status_code=500, detail="保存视频数据到数据库失败")

            # 重新查询已保存的数据并返回
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()
            else:
                raise HTTPException(status_code=500, detail="保存后查询视频数据失败")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 方法4: 数据库 + 自动获取cookies，返回dict
    async def get_video_db_auto_cookies(self, video_id: str) -> Dict:
        """
        自动获取cookies并获取视频详情，返回数据库模型格式

        Args:
            video_id: 视频ID

        Returns:
            Dict: 数据库模型字典格式
        """
        try:
            # 先从数据库查询
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()

            # 数据库没有则获取cookies并调用RPC
            cookies = await self._get_cookies_from_db(Platform.DOUYIN)

            client = await self._get_douyin_client()
            request = VideoDetailRequest(aweme_id=video_id)

            # 传递cookies给RPC客户端
            rpc_response = await client.get_video_detail(request, cookies)

            # 转换数据格式
            aweme_data = self._convert_rpc_to_db_model(rpc_response)

            # 使用参考文件的方式保存到数据库
            from models.douyin.models import update_douyin_aweme

            success = await update_douyin_aweme(aweme_data)

            if not success:
                raise HTTPException(status_code=500, detail="保存视频数据到数据库失败")

            # 重新查询已保存的数据并返回
            db_video = await DouyinAweme.filter(aweme_id=video_id).first()
            if db_video:
                return await db_video.to_dict()
            else:
                raise HTTPException(status_code=500, detail="保存后查询视频数据失败")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取视频详情失败: {str(e)}")

    # 通用方法：根据参数选择执行策略
    async def get_video_detail(
        self, video_id: str, cookies: Optional[str] = None, response_type: str = "rpc"
    ) -> Union[VideoDetailResponse, Dict]:
        """
        通用视频详情获取方法

        Args:
            video_id: 视频ID
            cookies: 可选的cookies，如果不提供将从数据库获取
            response_type: 响应类型，"rpc" 或 "db"

        Returns:
            Union[VideoDetailResponse, Dict]: 根据response_type返回对应格式
        """
        if response_type == "rpc":
            if cookies:
                return await self.get_video_rpc_with_cookies(video_id, cookies)
            else:
                return await self.get_video_rpc_auto_cookies(video_id)
        elif response_type == "db":
            if cookies:
                return await self.get_video_db_with_cookies(video_id, cookies)
            else:
                return await self.get_video_db_auto_cookies(video_id)
        else:
            raise HTTPException(status_code=400, detail="不支持的响应类型，请使用 'rpc' 或 'db'")

    # 收藏夹相关方法（委托给收藏夹控制器，保持向后兼容性）
    async def get_self_aweme_collection_rpc_with_cookies(self, cursor: int = 0, count: int = 10, cookies: str = None):
        """委托给收藏夹控制器"""
        return await self.collection_controller.get_self_aweme_collection_rpc_with_cookies(cursor, count, cookies)

    async def get_collect_video_list_rpc_with_cookies(
        self, collects_id: str, cursor: int = 0, count: int = 10, cookies: str = None
    ):
        """委托给收藏夹控制器"""
        return await self.collection_controller.get_collect_video_list_rpc_with_cookies(
            collects_id, cursor, count, cookies
        )

    async def sync_and_save_single_collection_with_cookies(self, collection_id: str, cookies: str):
        """委托给收藏夹控制器"""
        return await self.collection_controller.sync_and_save_single_collection_with_cookies(collection_id, cookies)

    async def process_video_id(self, aweme_id: str) -> Dict:
        """
        处理视频ID并提取相关数据

        Args:
            aweme_id: 抖音视频ID

        Returns:
            Dict: 处理后的视频数据
        """
        video_controller = DouyinVideoController(self)
        return await video_controller.process_video_id(aweme_id)

    async def validate_cookies(self, cookies: str) -> Dict:
        """
        验证传入的Cookies是否可用

        Args:
            cookies: Cookie 字符串

        Returns:
            Dict: 验证结果
        """
        try:
            # 创建抖音客户端
            client = await self._get_douyin_client()

            # 使用pong方法验证cookies
            is_valid = await client.pong(cookies)

            if is_valid:
                return {"valid": True, "message": "Cookies验证成功"}
            else:
                return {"valid": False, "message": "Cookies验证失败"}

        except Exception as e:
            return {"valid": False, "message": f"Cookies验证失败: {str(e)}"}

    async def get_user_info_with_cookies(self, sec_user_id: str, cookies: str) -> Dict:
        """
        传入cookies获取用户信息，返回RPC原响应格式

        Args:
            sec_user_id: 用户的加密ID
            cookies: 抖音网站的Cookie字符串

        Returns:
            Dict: RPC原响应格式的用户信息
        """
        try:
            # 创建抖音客户端
            client = await self._get_douyin_client()

            # 创建用户信息请求
            from rpc.douyin.schemas import UserInfoRequest

            request = UserInfoRequest(sec_user_id=sec_user_id)

            # 调用RPC获取用户信息
            response = await client.get_user_info(request, cookies)

            # 将 Pydantic 模型转换为字典格式返回
            return response.model_dump()

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"获取用户信息失败: {str(e)}")

    async def discover_search(
        self, keyword: str, offset: int = 0, count: int = 20, search_channel: str = None, cookies: str = None
    ) -> DiscoverSearchResponse:
        """
        发现搜索

        Args:
            keyword: 搜索关键词
            offset: 分页偏移量
            count: 每页数量
            search_channel: 搜索频道
            cookies: 可选的 cookies 字符串

        Returns:
            DiscoverSearchResponse: 发现搜索响应结果
        """
        try:
            client = await self._get_douyin_client()
            request = DiscoverSearchRequest(keyword=keyword, offset=offset, count=count, search_channel=search_channel)

            # 使用提供的 cookies，如果没有则传递 None
            response = await client.discover_search(request, cookies)
            return response
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"发现搜索失败: {str(e)}")


# 创建控制器实例
douyin_controller = DouyinController()
