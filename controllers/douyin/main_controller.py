"""
抖音主控制器

整合所有抖音相关的控制器，提供统一的接口，包括：
- HTML方式获取数据 (jingxuan, mobile)
- RPC方式获取数据
- 智能选择最佳获取方式
- 统一的错误处理和日志记录
"""

from typing import Dict, List, Optional, Union

from fastapi import HTTPException
from loguru import logger

from mappers.douyin import FetcherConfig
from models.douyin.models import DouyinAweme
from rpc.douyin.schemas import DiscoverSearchResponse, VideoDetailResponse

from .controller import DouyinController as DouyinVideoController
from .html_controller import DouyinHTMLController
from .models import JingxuanDataFetchResult, MobileDataFetchResult, RPCDataFetchResult
from .services import DouyinDataService
from .video_fetcher_controller import VideoFetcherController


class DouyinController:
    """抖音主控制器 - 提供统一的抖音数据获取接口"""

    def __init__(self):
        """初始化主控制器"""
        self.html_controller = DouyinHTMLController()
        self.video_controller = DouyinVideoController()
        self.video_fetcher_controller = VideoFetcherController()
        self.data_service = DouyinDataService()
        self.logger = logger.bind(component="DouyinController")

    # HTML方式获取数据的方法
    async def fetch_jingxuan_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过精选页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source等字段的结果
        """
        self.logger.info(f"开始通过精选页面获取视频数据: {aweme_id}")

        return await self.html_controller.fetch_jingxuan_video_data(
            aweme_id=aweme_id,
            use_proxy=use_proxy,
            custom_headers=custom_headers,
            timeout=timeout,
            save_to_db=save_to_db,
        )

    async def fetch_mobile_data(
        self,
        aweme_id: str,
        use_proxy: bool = True,
        custom_headers: Optional[Dict[str, str]] = None,
        timeout: Optional[int] = None,
        save_to_db: bool = True,
    ) -> Dict:
        """
        通过移动端分享页面获取视频数据

        Args:
            aweme_id: 抖音视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 请求超时时间
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source等字段的结果
        """
        self.logger.info(f"开始通过移动端分享页面获取视频数据: {aweme_id}")

        return await self.html_controller.fetch_mobile_share_video_data(
            aweme_id=aweme_id,
            use_proxy=use_proxy,
            custom_headers=custom_headers,
            timeout=timeout,
            save_to_db=save_to_db,
        )

    # RPC方式获取数据的方法
    async def fetch_rpc_data(
        self, aweme_id: str, cookies: Optional[str] = None, response_type: str = "db"
    ) -> Union[VideoDetailResponse, Dict]:
        """
        通过RPC方式获取视频数据

        Args:
            aweme_id: 抖音视频ID
            cookies: 可选的cookies，如果不提供将自动从数据库获取
            response_type: 响应类型，"rpc" 或 "db"

        Returns:
            Union[VideoDetailResponse, Dict]: 根据response_type返回对应格式
        """
        self.logger.info(f"开始通过RPC方式获取视频数据: {aweme_id}")

        return await self.video_controller.get_video_detail(
            video_id=aweme_id, cookies=cookies, response_type=response_type
        )

    # 新的统一获取方法（推荐使用）
    async def fetch_video_data_unified(
        self,
        aweme_id: str,
        preferred_methods: Optional[List[str]] = None,
        config: Optional[FetcherConfig] = None,
        use_proxy: bool = True,
        save_to_db: bool = True,
    ) -> Dict:
        """
        使用新的统一视频获取器获取数据

        Args:
            aweme_id: 抖音视频ID
            preferred_methods: 首选方法列表 ["mobile", "jingxuan", "rpc"]
            config: 获取器配置，如果不提供则使用默认配置
            use_proxy: 是否使用代理
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source、method等字段的结果
        """
        self.logger.info(f"使用统一获取器获取视频数据: {aweme_id}")

        if config is None:
            # 创建默认配置
            config = FetcherConfig(
                preferred_methods=preferred_methods or ["mobile", "jingxuan", "rpc"],
                fallback_enabled=True,
                retry_attempts=2,
                retry_delay=1.0,
                timeout=30,
                use_proxy=use_proxy,
                save_to_db=save_to_db,
            )

        try:
            # 使用新的VideoFetcherController
            result = await self.video_fetcher_controller.fetch_video_data(aweme_id, config)

            # 转换为与现有接口兼容的格式
            return {
                "success": result.success,
                "data": result.data,
                "source": result.source,
                "method": result.method_used,
                "aweme_id": aweme_id,
                "fetch_time": result.fetch_time.isoformat() if result.fetch_time else None,
                "errors": [str(e) for e in result.errors] if result.errors else None,
            }

        except Exception as e:
            self.logger.error(f"统一获取器获取失败: {e}")
            return {
                "success": False,
                "aweme_id": aweme_id,
                "error": str(e),
                "method": "unified_fetcher",
            }

    # 批量获取方法（新版本）
    async def batch_fetch_video_data_unified(
        self,
        aweme_ids: List[str],
        config: Optional[FetcherConfig] = None,
        max_concurrent: int = 5,
    ) -> List[Dict]:
        """
        使用新的统一获取器批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            config: 获取器配置
            max_concurrent: 最大并发数

        Returns:
            List[Dict]: 结果列表
        """
        self.logger.info(f"使用统一获取器批量获取: {len(aweme_ids)}个视频")

        if config is None:
            # 创建默认配置
            config = FetcherConfig(
                preferred_methods=["mobile", "jingxuan", "rpc"],
                fallback_enabled=True,
                retry_attempts=2,
                retry_delay=1.0,
                timeout=30,
                max_concurrent=max_concurrent,
            )

        try:
            # 使用新的VideoFetcherController
            batch_result = await self.video_fetcher_controller.batch_fetch(aweme_ids, config)

            # 转换为与现有接口兼容的格式
            results = []
            for result in batch_result.results:
                results.append(
                    {
                        "success": result.success,
                        "data": result.data,
                        "source": result.source,
                        "method": result.method_used,
                        "aweme_id": result.aweme_id,
                        "fetch_time": result.fetch_time.isoformat() if result.fetch_time else None,
                        "errors": [str(e) for e in result.errors] if result.errors else None,
                    }
                )

            self.logger.info(f"统一获取器批量完成: {batch_result.success_count}/{len(aweme_ids)} 成功")
            return results

        except Exception as e:
            self.logger.error(f"统一获取器批量获取失败: {e}")
            # 返回失败结果
            return [
                {
                    "success": False,
                    "aweme_id": aweme_id,
                    "error": str(e),
                    "method": "unified_fetcher",
                }
                for aweme_id in aweme_ids
            ]

    # 智能获取方法（保留向后兼容性）
    async def fetch_video_data_auto(
        self,
        aweme_id: str,
        preferred_method: str = "jingxuan",
        fallback_methods: Optional[List[str]] = None,
        use_proxy: bool = True,
        save_to_db: bool = True,
    ) -> Dict:
        """
        智能获取视频数据，支持多种方法和回退机制

        Args:
            aweme_id: 抖音视频ID
            preferred_method: 首选方法 ("jingxuan", "mobile", "rpc")
            fallback_methods: 回退方法列表，如果首选方法失败则依次尝试
            use_proxy: 是否使用代理
            save_to_db: 是否保存到数据库

        Returns:
            Dict: 包含success、data、source、method等字段的结果
        """
        if fallback_methods is None:
            fallback_methods = ["mobile", "rpc"]

        self.logger.info(f"智能获取视频数据: {aweme_id}, 首选方法: {preferred_method}")

        # 先从数据库查询
        try:
            db_video = await DouyinAweme.filter(aweme_id=aweme_id).first()
            if db_video:
                self.logger.info(f"从数据库获取到视频数据: {aweme_id}")
                return {
                    "success": True,
                    "data": await db_video.to_dict(),
                    "source": "database",
                    "method": "database",
                    "aweme_id": aweme_id,
                }
        except Exception as e:
            self.logger.warning(f"查询数据库失败: {e}")

        # 构建方法列表
        methods = [preferred_method] + [m for m in fallback_methods if m != preferred_method]

        last_error = None

        for method in methods:
            try:
                self.logger.info(f"尝试使用方法: {method}")

                if method == "jingxuan":
                    result = await self.fetch_jingxuan_data(
                        aweme_id=aweme_id, use_proxy=use_proxy, save_to_db=save_to_db
                    )
                elif method == "mobile":
                    result = await self.fetch_mobile_data(aweme_id=aweme_id, use_proxy=use_proxy, save_to_db=save_to_db)
                elif method == "rpc":
                    rpc_result = await self.fetch_rpc_data(aweme_id=aweme_id, response_type="db")
                    if isinstance(rpc_result, dict):
                        result = {
                            "success": True,
                            "data": rpc_result,
                            "source": "rpc_api",
                            "method": method,
                            "aweme_id": aweme_id,
                        }
                    else:
                        result = {"success": False, "method": method}
                else:
                    self.logger.warning(f"不支持的方法: {method}")
                    continue

                if result.get("success", False):
                    result["method"] = method
                    self.logger.info(f"方法 {method} 获取成功: {aweme_id}")
                    return result
                else:
                    last_error = result.get("error", f"方法 {method} 失败")
                    self.logger.warning(f"方法 {method} 失败: {last_error}")

            except Exception as e:
                last_error = str(e)
                self.logger.error(f"方法 {method} 异常: {e}")
                continue

        # 所有方法都失败
        self.logger.error(f"所有方法都失败，无法获取视频数据: {aweme_id}")
        return {
            "success": False,
            "aweme_id": aweme_id,
            "error": f"所有获取方法都失败: {last_error}",
            "methods_tried": methods,
        }

    # 批量获取方法
    async def batch_fetch_video_data(
        self, aweme_ids: List[str], method: str = "auto", max_concurrent: int = 5, use_proxy: bool = True
    ) -> List[Dict]:
        """
        批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            method: 获取方法 ("auto", "jingxuan", "mobile", "rpc")
            max_concurrent: 最大并发数
            use_proxy: 是否使用代理

        Returns:
            List[Dict]: 结果列表
        """
        import asyncio
        from asyncio import Semaphore

        self.logger.info(f"开始批量获取视频数据: {len(aweme_ids)}个视频, 方法: {method}")

        # 创建信号量控制并发
        semaphore = Semaphore(max_concurrent)

        async def fetch_single_video(aweme_id: str) -> Dict:
            async with semaphore:
                try:
                    if method == "auto":
                        return await self.fetch_video_data_auto(aweme_id, use_proxy=use_proxy)
                    elif method == "jingxuan":
                        return await self.fetch_jingxuan_data(aweme_id, use_proxy=use_proxy)
                    elif method == "mobile":
                        return await self.fetch_mobile_data(aweme_id, use_proxy=use_proxy)
                    elif method == "rpc":
                        rpc_result = await self.fetch_rpc_data(aweme_id, response_type="db")
                        if isinstance(rpc_result, dict):
                            return {
                                "success": True,
                                "data": rpc_result,
                                "source": "rpc_api",
                                "method": method,
                                "aweme_id": aweme_id,
                            }
                        else:
                            return {"success": False, "aweme_id": aweme_id, "method": method}
                    else:
                        return {
                            "success": False,
                            "aweme_id": aweme_id,
                            "error": f"不支持的方法: {method}",
                            "method": method,
                        }
                except Exception as e:
                    self.logger.error(f"批量获取视频 {aweme_id} 失败: {e}")
                    return {"success": False, "aweme_id": aweme_id, "error": str(e), "method": method}

        # 执行批量任务
        tasks = [fetch_single_video(aweme_id) for aweme_id in aweme_ids]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(
                    {"success": False, "aweme_id": aweme_ids[i], "error": str(result), "method": method}
                )
            else:
                processed_results.append(result)

        success_count = sum(1 for r in processed_results if r.get("success", False))
        self.logger.info(f"批量获取完成: {success_count}/{len(aweme_ids)} 成功")

        return processed_results

    # 委托给视频控制器的方法
    async def validate_cookies(self, cookies: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.validate_cookies(cookies)

    async def process_video_id(self, video_id: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.process_video_id(video_id)

    async def get_video_rpc_with_cookies(self, video_id: str, cookies: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.get_video_rpc_with_cookies(video_id, cookies)

    async def get_video_rpc_auto_cookies(self, video_id: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.get_video_rpc_auto_cookies(video_id)

    async def get_video_db_with_cookies(self, video_id: str, cookies: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.get_video_db_with_cookies(video_id, cookies)

    async def get_video_db_auto_cookies(self, video_id: str):
        """代理到 video_controller 的方法"""
        return await self.video_controller.get_video_db_auto_cookies(video_id)

    async def get_self_aweme_collection_rpc_with_cookies(self, cursor: int, count: int, cookies: str):
        """获取用户收藏夹列表（RPC响应格式）"""
        try:
            from rpc.douyin.schemas import SelfAwemeCollectionRequest

            # 获取抖音客户端
            client = await self.video_controller._get_douyin_client()

            # 构建请求参数
            request = SelfAwemeCollectionRequest(cursor=cursor, count=count)

            # 使用传入的cookies调用API
            response = await client.get_self_aweme_collection(request, cookies=cookies)

            return response

        except Exception as e:
            self.logger.error(f"获取收藏夹列表失败: {str(e)}")

            # 如果是验证错误，记录更详细的信息
            if "validation" in str(e).lower() or "required" in str(e).lower():
                self.logger.error(f"响应数据格式验证失败，可能API返回的数据结构不完整: {str(e)}")

            # 如果API调用失败，返回错误响应
            from rpc.douyin.schemas import SelfAwemeCollectionResponse

            return SelfAwemeCollectionResponse(status_code=-1, has_more=False, cursor=cursor, collects_list=[])

    async def get_collect_video_list_rpc_with_cookies(self, collects_id: str, cursor: int, count: int, cookies: str):
        """获取收藏视频列表（RPC响应格式）"""
        try:
            from rpc.douyin.collection_api import async_douyin_collection_api
            from rpc.douyin.schemas import CollectVideoListRequest

            # 获取抖音收藏客户端
            client = async_douyin_collection_api

            # 构建请求参数
            request = CollectVideoListRequest(collects_id=collects_id, cursor=cursor, count=count)

            # 使用传入的cookies调用API
            response = await client.get_collect_video_list(request, cookies=cookies)

            return response

        except Exception as e:
            self.logger.error(f"获取收藏视频列表失败: {str(e)}")
            # 如果API调用失败，返回错误响应
            from rpc.douyin.schemas import CollectVideoListResponse

            return CollectVideoListResponse(status_code=-1, has_more=False, max_cursor=cursor, aweme_list=[])

    async def sync_single_collection_with_cookies(self, collection_id: str, cookies: str):
        """同步指定收藏夹和视频数据"""
        try:
            result = {
                "collections_synced": 0,
                "videos_synced": 0,
                "collections_filtered": 0,
                "relations_created": 0,
                "relations_existing": 0,
                "trendinsight_relations_created": 0,
                "trendinsight_relations_existing": 0,
                "aweme_ids": [],
                "errors": [],
            }

            # 1. 获取收藏夹视频列表
            try:
                # 分页获取所有视频
                cursor = 0
                count = 20
                max_pages = 50  # 最多获取50页，避免无限循环
                all_videos = []

                for _ in range(max_pages):
                    videos_response = await self.get_collect_video_list_rpc_with_cookies(
                        collection_id, cursor, count, cookies
                    )

                    if videos_response.status_code != 0:
                        result["errors"].append(f"获取收藏夹视频失败: status_code={videos_response.status_code}")
                        break

                    if not videos_response.aweme_list:
                        break

                    all_videos.extend(videos_response.aweme_list)
                    result["videos_synced"] += len(videos_response.aweme_list)

                    # 检查是否还有更多数据
                    if not videos_response.has_more:
                        break

                    cursor = videos_response.max_cursor

                # 提取视频ID
                aweme_ids = []
                for video in all_videos:
                    if isinstance(video, dict):
                        video_id = video.get("aweme_id")
                    else:
                        video_id = getattr(video, "aweme_id", None)

                    if video_id:
                        aweme_ids.append(video_id)

                result["aweme_ids"] = aweme_ids
                result["collections_synced"] = 1 if aweme_ids else 0

                # 2. 这里可以添加更多的同步逻辑，比如：
                # - 将视频数据保存到数据库
                # - 创建关联关系
                # - 与TrendInsight关联等

                self.logger.info(f"成功同步收藏夹 {collection_id}，获取到 {len(aweme_ids)} 个视频")

            except Exception as e:
                error_msg = f"同步收藏夹失败: {str(e)}"
                result["errors"].append(error_msg)
                self.logger.error(error_msg)

            return result

        except Exception as e:
            self.logger.error(f"sync_single_collection_with_cookies 执行失败: {str(e)}")
            return {
                "collections_synced": 0,
                "videos_synced": 0,
                "collections_filtered": 0,
                "relations_created": 0,
                "relations_existing": 0,
                "trendinsight_relations_created": 0,
                "trendinsight_relations_existing": 0,
                "aweme_ids": [],
                "errors": [f"同步失败: {str(e)}"],
            }

    async def sync_and_save_single_collection_with_cookies(self, collection_id: str, cookies: str):
        """同步指定收藏夹和视频数据并保存到数据库"""
        try:
            result = {
                "collections_synced": 0,
                "videos_synced": 0,
                "collections_filtered": 0,
                "relations_created": 0,
                "relations_existing": 0,
                "trendinsight_relations_created": 0,
                "trendinsight_relations_existing": 0,
                "aweme_ids": [],
                "errors": [],
            }

            # 1. 获取收藏夹视频列表
            try:
                # 分页获取所有视频
                cursor = 0
                count = 20
                max_pages = 50  # 最多获取50页，避免无限循环
                all_videos = []

                for _ in range(max_pages):
                    videos_response = await self.get_collect_video_list_rpc_with_cookies(
                        collection_id, cursor, count, cookies
                    )

                    if videos_response.status_code != 0:
                        result["errors"].append(f"获取收藏夹视频失败: status_code={videos_response.status_code}")
                        break

                    if not videos_response.aweme_list:
                        break

                    all_videos.extend(videos_response.aweme_list)
                    result["videos_synced"] += len(videos_response.aweme_list)

                    # 检查是否还有更多数据
                    if not videos_response.has_more:
                        break

                    cursor = videos_response.max_cursor

                # 提取视频ID
                aweme_ids = []
                for video in all_videos:
                    if isinstance(video, dict):
                        video_id = video.get("aweme_id")
                    else:
                        video_id = getattr(video, "aweme_id", None)

                    if video_id:
                        aweme_ids.append(video_id)

                result["aweme_ids"] = aweme_ids
                result["collections_synced"] = 1 if aweme_ids else 0

                # 2. 这里可以添加更多的同步逻辑，比如：
                # - 将视频数据保存到数据库
                # - 创建关联关系
                # - 与TrendInsight关联等

                self.logger.info(f"成功同步收藏夹 {collection_id}，获取到 {len(aweme_ids)} 个视频")

            except Exception as e:
                error_msg = f"同步收藏夹失败: {str(e)}"
                result["errors"].append(error_msg)
                self.logger.error(error_msg)

            return result

        except Exception as e:
            self.logger.error(f"sync_and_save_single_collection_with_cookies 执行失败: {str(e)}")
            return {
                "collections_synced": 0,
                "videos_synced": 0,
                "collections_filtered": 0,
                "relations_created": 0,
                "relations_existing": 0,
                "trendinsight_relations_created": 0,
                "trendinsight_relations_existing": 0,
                "aweme_ids": [],
                "errors": [f"同步失败: {str(e)}"],
            }

    # 获取底层控制器的方法（用于高级用法）
    def get_html_controller(self) -> DouyinHTMLController:
        """获取HTML控制器实例"""
        return self.html_controller

    def get_video_controller(self) -> DouyinVideoController:
        """获取视频控制器实例"""
        return self.video_controller

    def get_video_fetcher_controller(self) -> VideoFetcherController:
        """获取新的统一视频获取器实例"""
        return self.video_fetcher_controller

    # 强类型步骤式获取方法
    async def fetch_mobile_data_typed(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> MobileDataFetchResult:
        """
        使用强类型步骤式获取移动端数据

        包含3个明确步骤:
        1. 获取HTML内容
        2. 解析HTML中的JSON
        3. 转换为数据库模型

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            MobileDataFetchResult: 完整的获取结果，包含每个步骤的详细信息
        """
        self.logger.info(f"开始强类型获取移动端数据: {aweme_id}")
        return await self.data_service.fetch_mobile_data(
            aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
        )

    async def fetch_jingxuan_data_typed(
        self, aweme_id: str, use_proxy: bool = True, custom_headers: Optional[Dict[str, str]] = None, timeout: int = 30
    ) -> JingxuanDataFetchResult:
        """
        使用强类型步骤式获取精选页面数据

        包含3个明确步骤:
        1. 获取HTML内容
        2. 解析HTML中的JSON
        3. 转换为数据库模型

        Args:
            aweme_id: 视频ID
            use_proxy: 是否使用代理
            custom_headers: 自定义请求头
            timeout: 超时时间

        Returns:
            JingxuanDataFetchResult: 完整的获取结果，包含每个步骤的详细信息
        """
        self.logger.info(f"开始强类型获取精选页面数据: {aweme_id}")
        return await self.data_service.fetch_jingxuan_data(
            aweme_id=aweme_id, use_proxy=use_proxy, custom_headers=custom_headers, timeout=timeout
        )

    async def fetch_rpc_data_typed(self, aweme_id: str, cookies: Optional[str] = None) -> RPCDataFetchResult:
        """
        使用强类型步骤式获取RPC数据

        包含2个明确步骤:
        1. 调用RPC接口
        2. 转换为数据库模型

        Args:
            aweme_id: 视频ID
            cookies: 可选的cookies

        Returns:
            RPCDataFetchResult: 完整的获取结果，包含每个步骤的详细信息
        """
        self.logger.info(f"开始强类型获取RPC数据: {aweme_id}")
        return await self.data_service.fetch_rpc_data(aweme_id=aweme_id, cookies=cookies)

    async def discover_search(
        self, keyword: str, offset: int = 0, count: int = 20, search_channel: str = None, cookies: str = None
    ) -> DiscoverSearchResponse:
        """
        发现搜索

        Args:
            keyword: 搜索关键词
            offset: 分页偏移量
            count: 每页数量
            search_channel: 搜索频道
            cookies: 可选的 cookies 字符串

        Returns:
            DiscoverSearchResponse: 发现搜索响应结果
        """
        try:
            # 使用 video_controller 中的 discover_search 方法
            video_controller = DouyinVideoController()
            return await video_controller.discover_search(keyword, offset, count, search_channel, cookies)
        except Exception as e:
            self.logger.error(f"发现搜索失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"发现搜索失败: {str(e)}")

    def get_data_service(self) -> DouyinDataService:
        """获取数据服务实例"""
        return self.data_service


# 创建默认控制器实例
douyin_controller = DouyinController()
