"""
Unified video fetcher controller that integrates all three fetch methods.
"""

import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger

from mappers.douyin.exceptions import (
    ConfigurationException,
    FallbackExhaustedException,
    NetworkException,
)
from mappers.douyin.jingxuan_mapper import JingxuanDataMapper
from mappers.douyin.mobile_mapper import MobileDataMapper
from mappers.douyin.models import BatchFetchResult, FetcherConfig, FetchResult
from mappers.douyin.pydantic_models import DouyinVideoData
from mappers.douyin.rpc_mapper import RPCDataMapper


class VideoFetcherController:
    """
    统一的视频获取控制器，整合三种获取方法
    """

    def __init__(self, config: Optional[FetcherConfig] = None):
        """
        初始化视频获取控制器

        Args:
            config: 获取器配置
        """
        self.config = config or FetcherConfig()

        if not self.config.validate():
            raise ConfigurationException("Invalid fetcher configuration")

        # 初始化映射器
        self.mobile_mapper = MobileDataMapper()
        self.jingxuan_mapper = JingxuanDataMapper()
        self.rpc_mapper = RPCDataMapper()

        # 映射器字典
        self._mappers = {
            "mobile": self.mobile_mapper,
            "jingxuan": self.jingxuan_mapper,
            "rpc": self.rpc_mapper,
        }

        # 并发控制
        self._semaphore = asyncio.Semaphore(self.config.max_concurrent)

        # 性能统计
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "method_usage": {method: 0 for method in self._mappers.keys()},
            "method_success": {method: 0 for method in self._mappers.keys()},
        }

    async def fetch_video_data(
        self, aweme_id: str, method: str, options: Optional[Dict[str, Any]] = None
    ) -> FetchResult[DouyinVideoData]:
        """
        使用指定方法获取视频数据

        Args:
            aweme_id: 视频ID
            method: 获取方法 ("mobile", "jingxuan", "rpc")
            options: 额外选项

        Returns:
            FetchResult: 获取结果
        """
        if method not in self._mappers:
            raise ValueError(f"Unsupported method: {method}. Supported: {list(self._mappers.keys())}")

        result = FetchResult(success=False, aweme_id=aweme_id, method=method, source=method)

        self._stats["total_requests"] += 1
        self._stats["method_usage"][method] += 1

        try:
            # 使用信号量控制并发
            async with self._semaphore:
                raw_data = await self._fetch_raw_data(aweme_id, method, options)
                mapper = self._mappers[method]

                # 映射数据到标准格式
                standard_data: DouyinVideoData = mapper.map_to_standard_format(raw_data)

                # 保存到数据库（如果配置允许）
                if self.config.save_to_db:
                    await self._save_to_database(standard_data.model_dump())

                result.data = standard_data
                result.mark_completed(success=True)

                self._stats["successful_requests"] += 1
                self._stats["method_success"][method] += 1

                if self.config.enable_detailed_logging:
                    logger.info(f"Successfully fetched video data: {result.get_summary()}")

        except Exception as e:
            error_msg = f"Failed to fetch video data using {method}: {str(e)}"
            result.mark_completed(success=False, error=error_msg)
            result.exception_type = type(e).__name__

            self._stats["failed_requests"] += 1

            if self.config.enable_detailed_logging:
                logger.error(f"Failed to fetch video data: {result.get_summary()}")

            if self.config.fail_fast:
                raise

        return result

    async def fetch_with_fallback(
        self, aweme_id: str, methods: Optional[List[str]] = None, options: Optional[Dict[str, Any]] = None
    ) -> FetchResult[DouyinVideoData]:
        """
        使用回退策略获取视频数据

        Args:
            aweme_id: 视频ID
            methods: 要尝试的方法列表，如果为None则使用配置中的回退方法
            options: 额外选项

        Returns:
            FetchResult: 获取结果
        """
        if not self.config.enable_fallback:
            raise ConfigurationException("Fallback is disabled in configuration")

        if methods is None:
            methods = self.config.fallback_methods.copy()

        # 验证方法列表
        for method in methods:
            if method not in self._mappers:
                raise ValueError(f"Unsupported method: {method}")

        result: FetchResult[DouyinVideoData] = FetchResult(
            success=False, aweme_id=aweme_id, method="fallback", source="fallback"
        )

        method_errors = {}

        for method in methods:
            result.add_attempted_method(method)

            try:
                if self.config.enable_detailed_logging:
                    logger.info(f"Trying method {method} for aweme_id {aweme_id}")

                method_result = await self.fetch_video_data(aweme_id, method, options)

                if method_result.success:
                    # 成功获取数据，更新结果
                    result.data = method_result.data
                    result.method = method
                    result.source = method
                    result.response_time = method_result.response_time
                    result.mark_completed(success=True)

                    if self.config.enable_detailed_logging:
                        logger.info(f"Fallback succeeded with method {method}: {result.get_summary()}")

                    return result
                else:
                    method_errors[method] = method_result.error

            except Exception as e:
                error_msg = f"Method {method} failed: {str(e)}"
                method_errors[method] = error_msg

                if self.config.enable_detailed_logging:
                    logger.warning(f"Method {method} failed for aweme_id {aweme_id}: {error_msg}")

        # 所有方法都失败了
        error_msg = f"All fallback methods failed for aweme_id {aweme_id}"
        result.mark_completed(success=False, error=error_msg)
        result.metadata["method_errors"] = method_errors

        if self.config.enable_detailed_logging:
            logger.error(f"All fallback methods failed: {result.get_summary()}")

        if self.config.raise_on_all_failed:
            raise FallbackExhaustedException(
                error_msg, attempted_methods=result.attempted_methods, method_errors=method_errors
            )

        return result

    async def batch_fetch(
        self, aweme_ids: List[str], method: Optional[str] = None, options: Optional[Dict[str, Any]] = None
    ) -> BatchFetchResult[DouyinVideoData]:
        """
        批量获取视频数据

        Args:
            aweme_ids: 视频ID列表
            method: 获取方法，如果为None则使用回退策略
            options: 额外选项

        Returns:
            BatchFetchResult: 批量获取结果
        """
        batch_result: BatchFetchResult[DouyinVideoData] = BatchFetchResult(total_count=len(aweme_ids))

        if self.config.enable_detailed_logging:
            logger.info(f"Starting batch fetch for {len(aweme_ids)} videos")

        # 创建任务列表
        tasks = []
        for aweme_id in aweme_ids:
            if method:
                task = self.fetch_video_data(aweme_id, method, options)
            else:
                task = self.fetch_with_fallback(aweme_id, None, options)
            tasks.append(task)

        # 并发执行所有任务
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    # 创建失败结果
                    failed_result = FetchResult(
                        success=False,
                        aweme_id=aweme_ids[i],
                        method=method or "fallback",
                        source=method or "fallback",
                        error=str(result),
                        exception_type=type(result).__name__,
                    )
                    failed_result.mark_completed(success=False, error=str(result))
                    batch_result.add_result(failed_result)
                else:
                    batch_result.add_result(result)

        except Exception as e:
            logger.error(f"Batch fetch failed: {str(e)}")
            raise

        batch_result.mark_completed()

        if self.config.enable_detailed_logging:
            logger.info(f"Batch fetch completed: {batch_result.get_summary()}")

        return batch_result

    async def _fetch_raw_data(
        self, aweme_id: str, method: str, options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取原始数据（具体的获取逻辑需要根据现有控制器实现）

        Args:
            aweme_id: 视频ID
            method: 获取方法
            options: 额外选项

        Returns:
            Dict[str, Any]: 原始数据
        """
        options = options or {}

        if method == "mobile":
            return await self._fetch_mobile_data(aweme_id, options)
        elif method == "jingxuan":
            return await self._fetch_jingxuan_data(aweme_id, options)
        elif method == "rpc":
            return await self._fetch_rpc_data(aweme_id, options)
        else:
            raise ValueError(f"Unknown method: {method}")

    async def _fetch_mobile_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取移动端数据 - 使用强类型服务层

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: 移动端原始数据
        """
        from .services import DouyinDataService

        service = DouyinDataService()
        result = await service.fetch_mobile_data(
            aweme_id=aweme_id,
            use_proxy=options.get("use_proxy", True),
            custom_headers=options.get("custom_headers"),
            timeout=options.get("timeout", 30),
        )

        if result.success and result.final_model:
            # 将DouyinAweme模型转换为字典格式以保持兼容性
            model = result.final_model
            return {
                "aweme_id": model.aweme_id,
                "title": model.title,
                "desc": model.desc,
                "create_time": model.create_time,
                "author_name": model.author_name,
                "author_id": model.author_id,
                "like_count": int(model.liked_count) if model.liked_count else 0,
                "comment_count": int(model.comment_count) if model.comment_count else 0,
                "share_count": int(model.share_count) if model.share_count else 0,
                "collect_count": int(model.collect_count) if model.collect_count else 0,
                "duration": model.duration,
                "video_urls": model.video_url.split(",") if model.video_url else [],
                "cover_urls": model.cover_url.split(",") if model.cover_url else [],
            }
        else:
            error_msg = f"Mobile data fetch failed for {aweme_id}: {result.error_message}"
            if result.failed_step:
                error_msg += f" (failed at step: {result.failed_step})"
            raise NetworkException(error_msg, context={"result": result})

    async def _fetch_jingxuan_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取精选页面数据 - 使用强类型服务层

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: 精选页面原始数据
        """
        from .services import DouyinDataService

        service = DouyinDataService()
        result = await service.fetch_jingxuan_data(
            aweme_id=aweme_id,
            use_proxy=options.get("use_proxy", True),
            custom_headers=options.get("custom_headers"),
            timeout=options.get("timeout", 30),
        )

        if result.success and result.final_model:
            # 将DouyinAweme模型转换为字典格式以保持兼容性
            model = result.final_model
            return {
                "aweme_id": model.aweme_id,
                "title": model.title,
                "desc": model.desc,
                "create_time": model.create_time,
                "author_name": model.author_name,
                "author_id": model.author_id,
                "like_count": int(model.liked_count) if model.liked_count else 0,
                "comment_count": int(model.comment_count) if model.comment_count else 0,
                "share_count": int(model.share_count) if model.share_count else 0,
                "collect_count": int(model.collect_count) if model.collect_count else 0,
                "duration": model.duration,
                "video_urls": model.video_url.split(",") if model.video_url else [],
                "cover_urls": model.cover_url.split(",") if model.cover_url else [],
            }
        else:
            error_msg = f"Jingxuan data fetch failed for {aweme_id}: {result.error_message}"
            if result.failed_step:
                error_msg += f" (failed at step: {result.failed_step})"
            raise NetworkException(error_msg, context={"result": result})

    async def _fetch_rpc_data(self, aweme_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取RPC数据 - 使用强类型服务层

        Args:
            aweme_id: 视频ID
            options: 额外选项

        Returns:
            Dict[str, Any]: RPC原始数据
        """
        from .services import DouyinDataService

        service = DouyinDataService()
        result = await service.fetch_rpc_data(aweme_id=aweme_id, cookies=options.get("cookies"))

        if result.success and result.final_model:
            # 将DouyinAweme模型转换为字典格式以保持兼容性
            model = result.final_model
            return {
                "aweme_id": model.aweme_id,
                "title": model.title,
                "desc": model.desc,
                "create_time": model.create_time,
                "author_name": model.author_name,
                "author_id": model.author_id,
                "like_count": int(model.liked_count) if model.liked_count else 0,
                "comment_count": int(model.comment_count) if model.comment_count else 0,
                "share_count": int(model.share_count) if model.share_count else 0,
                "collect_count": int(model.collect_count) if model.collect_count else 0,
                "duration": model.duration,
                "video_urls": model.video_url.split(",") if model.video_url else [],
                "cover_urls": model.cover_url.split(",") if model.cover_url else [],
            }
        else:
            error_msg = f"RPC data fetch failed for {aweme_id}: {result.error_message}"
            if result.failed_step:
                error_msg += f" (failed at step: {result.failed_step})"
            raise NetworkException(error_msg, context={"result": result})

    async def _save_to_database(self, data: Dict[str, Any]) -> None:
        """
        保存数据到数据库

        Args:
            data: 标准格式的数据
        """
        try:
            from models.douyin.models import update_douyin_aweme

            await update_douyin_aweme(data)
        except Exception as e:
            logger.error(f"Failed to save data to database: {str(e)}")
            # 不抛出异常，避免影响主要流程

    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        stats = self._stats.copy()

        # 计算成功率
        if stats["total_requests"] > 0:
            stats["success_rate"] = stats["successful_requests"] / stats["total_requests"]
        else:
            stats["success_rate"] = 0.0

        # 计算各方法的成功率
        for method in self._mappers.keys():
            usage = stats["method_usage"][method]
            success = stats["method_success"][method]
            if usage > 0:
                stats[f"{method}_success_rate"] = success / usage
            else:
                stats[f"{method}_success_rate"] = 0.0

        return stats

    def reset_stats(self) -> None:
        """
        重置统计信息
        """
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "method_usage": {method: 0 for method in self._mappers.keys()},
            "method_success": {method: 0 for method in self._mappers.keys()},
        }
