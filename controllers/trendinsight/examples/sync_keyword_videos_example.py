#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关键词同步控制器示例调用代码

展示如何使用KeywordSyncController的sync_keyword_videos方法
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 数据库初始化
from tortoise import Tortoise

from controllers.trendinsight.keyword_sync_controller import KeywordSyncController
from settings.config import settings


async def init_db():
    """初始化数据库连接"""
    await Tortoise.init(config=settings.tortoise_orm)


async def close_db():
    """关闭数据库连接"""
    await Tortoise.close_connections()


async def main():
    """示例：同步关键词视频数据"""
    # 初始化数据库连接
    await init_db()

    try:
        # 创建控制器实例
        controller = KeywordSyncController()

        # 要同步的关键词
        keyword = "科技前沿"

        print(f"开始同步关键词 '{keyword}' 的视频数据...")

        try:
            # 调用同步方法
            result = await controller.sync_keyword_videos(keyword)

            # 输出结果
            print("\n=== 同步结果 ===")
            print(f"关键词操作类型: {result.keyword_action}")
            print(f"同步视频数量: {result.videos_synced}")
            print(f"同步失败数量: {result.videos_failed}")
            print(f"新创建关联数: {result.relations_created}")
            print(f"已存在关联数: {result.relations_existing}")
            print(f"错误数量: {len(result.errors)}")

            if result.keyword_data:
                print(f"\n=== 关键词信息 ===")
                print(f"ID: {result.keyword_data.id}")
                print(f"关键词: {result.keyword_data.keyword}")
                print(f"视频总数: {result.keyword_data.video_count}")
                print(f"创建时间: {result.keyword_data.created_at}")
                print(f"更新时间: {result.keyword_data.updated_at}")

            if result.video_items:
                print(f"\n=== 视频列表 (前3个) ===")
                for i, video in enumerate(result.video_items[:3]):
                    print(f"{i+1}. 视频ID: {video.aweme_id}")
                    print(f"   标题: {video.title or 'N/A'}")
                    print(f"   发布时间: {video.create_time}")
                    print(f"   点赞数: {video.liked_count}")
                    print(f"   评论数: {video.comment_count}")
                    print(f"   分享数: {video.share_count}")
                    print(f"   收藏数: {video.collected_count}")
                    print(f"   作者: {video.nickname or 'N/A'}")
                    print()

            if result.video_items:
                print(f"\n=== 视频列表 (前3个) ===")
                for i, video in enumerate(result.video_items[:3]):
                    print(f"{i+1}. 视频ID: {video.aweme_id}")
                    print(f"   标题: {video.title or 'N/A'}")
                    print(f"   发布时间: {video.create_time}")
                    print(f"   点赞数: {video.liked_count}")
                    print(f"   评论数: {video.comment_count}")
                    print(f"   分享数: {video.share_count}")
                    print(f"   收藏数: {video.collected_count}")
                    print(f"   作者: {video.nickname or 'N/A'}")
                    print()

            if result.errors:
                print(f"\n=== 错误信息 ===")
                for i, error in enumerate(result.errors):
                    print(f"{i+1}. {error}")

        except Exception as e:
            print(f"同步过程中发生错误: {e}")
            import traceback

            traceback.print_exc()

    finally:
        # 关闭数据库连接
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())
