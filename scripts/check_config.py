#!/usr/bin/env python3
"""
简单的配置检查脚本
"""

import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

import json
from settings.config import settings

def main():
    print("🔍 配置检查")
    print("=" * 50)
    
    # 检查环境
    env = getattr(settings, 'current_env', 'unknown')
    print(f"当前环境: {env}")
    
    # 检查 TORTOISE_ORM 配置
    print("\n📋 TORTOISE_ORM 配置:")
    try:
        tortoise_config = settings.TORTOISE_ORM
        print(json.dumps(dict(tortoise_config), indent=2, default=str))
    except Exception as e:
        print(f"❌ 获取配置失败: {e}")
    
    # 检查连接配置
    print("\n🔗 连接配置:")
    try:
        connections = dict(settings.TORTOISE_ORM.connections)
        for name, uri in connections.items():
            print(f"  - {name}: {uri}")
    except Exception as e:
        print(f"❌ 获取连接配置失败: {e}")
    
    # 检查应用配置
    print("\n📦 应用配置:")
    try:
        apps = dict(settings.TORTOISE_ORM.apps)
        for app_name, app_config in apps.items():
            models = app_config.get('models', [])
            default_conn = app_config.get('default_connection', 'N/A')
            print(f"  - {app_name}: {len(models)} 个模型, 连接: {default_conn}")
            for model in models:
                print(f"    * {model}")
    except Exception as e:
        print(f"❌ 获取应用配置失败: {e}")

if __name__ == "__main__":
    main()
