#!/usr/bin/env python3
"""
Tortoise-ORM 多数据源使用示例

本示例演示如何在多数据源环境中使用 UserInboxSourceRelated 和 UserInboxVideoRelated 模型
"""

import asyncio
import uuid
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))

from tortoise import Tortoise

# 导入配置
from settings.config import settings

# 导入奇好助手模型（来自独立数据源）
from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated

# 导入其他模型（来自默认数据源）
from models.douyin.models import DouyinAweme


async def init_database():
    """初始化数据库连接"""
    print("🔧 正在初始化数据库连接...")
    
    # 使用配置文件中的多数据源配置
    await Tortoise.init(config=settings.TORTOISE_ORM)
    
    # 生成数据库表结构（如果不存在）
    await Tortoise.generate_schemas()
    
    print("✅ 数据库连接初始化完成")

    # 获取连接信息
    try:
        connections = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
    except:
        connections = list(settings.TORTOISE_ORM["connections"].keys())

    print(f"📊 已连接的数据库: {connections}")


async def demonstrate_multi_database_operations():
    """演示多数据源操作"""
    
    print("\n🎯 开始多数据源操作演示")
    print("=" * 50)
    
    # 1. 在奇好助手数据库中创建用户收件箱关联记录
    print("\n📝 1. 创建用户收件箱来源关联记录（qihaozhushou 数据库）")
    
    user_uuid = str(uuid.uuid4()).replace('-', '')
    source_id = str(uuid.uuid4()).replace('-', '')
    
    source_related = await UserInboxSourceRelated.create(
        uuid=str(uuid.uuid4()).replace('-', ''),
        user_uuid=user_uuid,
        source_id=source_id,
        source_type="author"
    )
    
    print(f"✅ 创建来源关联: {source_related}")
    
    # 2. 在奇好助手数据库中创建视频关联记录
    print("\n📝 2. 创建用户收件箱视频关联记录（qihaozhushou 数据库）")
    
    video_id = f"video_{uuid.uuid4().hex[:16]}"
    
    video_related = await UserInboxVideoRelated.create(
        uuid=str(uuid.uuid4()).replace('-', ''),
        user_uuid=user_uuid,
        video_id=video_id,
        source_type="author"
    )
    
    print(f"✅ 创建视频关联: {video_related}")
    
    # 3. 查询奇好助手数据库中的记录
    print("\n🔍 3. 查询奇好助手数据库记录")
    
    # 查询用户的所有来源关联
    user_sources = await UserInboxSourceRelated.filter(
        user_uuid=user_uuid,
        is_deleted=False
    ).all()
    
    print(f"📊 用户 {user_uuid[:8]}... 的来源关联数量: {len(user_sources)}")
    
    # 查询用户的所有视频关联
    user_videos = await UserInboxVideoRelated.filter(
        user_uuid=user_uuid,
        is_deleted=False
    ).all()
    
    print(f"📊 用户 {user_uuid[:8]}... 的视频关联数量: {len(user_videos)}")
    
    # 4. 演示跨数据库查询（注意：这需要在应用层面进行关联）
    print("\n🔗 4. 跨数据库数据关联示例")
    
    # 假设我们要关联抖音视频数据（来自默认数据库）
    # 注意：这里只是演示概念，实际使用时需要确保数据存在
    try:
        # 查询默认数据库中的抖音视频（如果存在）
        douyin_videos_count = await DouyinAweme.all().count()
        print(f"📊 默认数据库中的抖音视频数量: {douyin_videos_count}")

        # 在应用层面关联数据
        print("💡 跨数据库关联需要在应用层面进行，例如：")
        print("   - 先从 qihaozhushou 数据库查询用户关联的 video_id")
        print("   - 再从 default 数据库查询对应的视频详情")
        
    except Exception as e:
        print(f"⚠️  查询默认数据库时出错（可能表不存在）: {e}")
    
    # 5. 演示事务操作（单数据库内）
    print("\n💾 5. 数据库事务操作示例")
    
    try:
        # 在奇好助手数据库中执行事务
        from tortoise.transactions import in_transaction
        
        async with in_transaction("qihaozhushou") as conn:
            # 在事务中创建多个关联记录
            new_source = await UserInboxSourceRelated.create(
                uuid=str(uuid.uuid4()).replace('-', ''),
                user_uuid=user_uuid,
                source_id=str(uuid.uuid4()).replace('-', ''),
                source_type="keyword",
                using_db=conn
            )
            
            new_video = await UserInboxVideoRelated.create(
                uuid=str(uuid.uuid4()).replace('-', ''),
                user_uuid=user_uuid,
                video_id=f"video_{uuid.uuid4().hex[:16]}",
                source_type="keyword",
                using_db=conn
            )
            
            print(f"✅ 事务中创建记录: {new_source.uuid[:8]}..., {new_video.uuid[:8]}...")
            
    except Exception as e:
        print(f"❌ 事务操作失败: {e}")


async def demonstrate_connection_usage():
    """演示如何获取和使用特定数据库连接"""
    
    print("\n🔌 数据库连接使用演示")
    print("=" * 30)
    
    # 获取所有连接名称
    try:
        connections = list(Tortoise._connections.keys()) if hasattr(Tortoise, '_connections') else []
    except:
        connections = list(settings.TORTOISE_ORM["connections"].keys())

    print(f"📋 可用连接: {connections}")
    
    # 获取特定连接
    default_conn = Tortoise.get_connection("default")
    qihaozhushou_conn = Tortoise.get_connection("qihaozhushou")
    
    print(f"🔗 默认连接: {default_conn}")
    print(f"🔗 奇好助手连接: {qihaozhushou_conn}")
    
    # 检查连接状态
    try:
        # 执行简单查询测试连接
        result = await qihaozhushou_conn.execute_query("SELECT 1 as test")
        print(f"✅ 奇好助手数据库连接正常: {result}")
    except Exception as e:
        print(f"❌ 奇好助手数据库连接异常: {e}")


async def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        # 删除测试创建的记录
        deleted_sources = await UserInboxSourceRelated.all().delete()
        deleted_videos = await UserInboxVideoRelated.all().delete()
        
        print(f"🗑️  已删除 {deleted_sources} 条来源关联记录")
        print(f"🗑️  已删除 {deleted_videos} 条视频关联记录")
        
    except Exception as e:
        print(f"⚠️  清理数据时出错: {e}")


async def main():
    """主函数"""
    print("🚀 Tortoise-ORM 多数据源使用示例")
    print("=" * 60)
    
    try:
        # 初始化数据库
        await init_database()
        
        # 演示多数据源操作
        await demonstrate_multi_database_operations()
        
        # 演示连接使用
        await demonstrate_connection_usage()
        
        # 清理测试数据
        await cleanup_test_data()
        
        print("\n🎉 多数据源操作演示完成！")
        
    except Exception as e:
        print(f"❌ 执行过程中出错: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 关闭数据库连接
        await Tortoise.close_connections()
        print("🔧 数据库连接已关闭")


if __name__ == "__main__":
    asyncio.run(main())
