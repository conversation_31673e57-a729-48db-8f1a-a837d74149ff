# 配置架构说明

## 📋 配置文件分层设计

### 设计原则

1. **应用配置不区分环境** - 模型组织方式在所有环境中都相同
2. **连接配置区分环境** - 不同环境使用不同的数据库连接
3. **环境变量优先级最高** - 本地开发可通过 `.env` 覆盖

### 配置文件职责

| 文件 | 职责 | 包含内容 |
|------|------|----------|
| `default.toml` | 基础配置 | 应用配置 + 默认连接配置 |
| `development.toml` | 开发环境 | 开发环境连接配置 |
| `staging.toml` | 预发布环境 | 预发布环境连接配置 |
| `production.toml` | 生产环境 | 生产环境连接配置 |
| `.env` | 本地覆盖 | 开发环境连接覆盖 |

## 🏗️ 配置结构

### 1. 应用配置 (default.toml)

```toml
# 应用配置（所有环境共享，不区分测试/正式环境）
[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"
```

**特点**:
- ✅ 所有环境共享
- ✅ 模型组织方式固定
- ✅ 不需要在各环境文件中重复

### 2. 连接配置 (各环境文件)

#### development.toml
```toml
[tortoise_orm.connections]
default = "sqlite://db.sqlite3"
qihaozhushou = "sqlite://qihaozhushou_dev.sqlite3"
```

#### staging.toml
```toml
[tortoise_orm.connections]
default = "sqlite://staging.sqlite3"
qihaozhushou = "mysql://qihaozhushou:password@host:3306/qihaozhushou_staging"
```

#### production.toml
```toml
[tortoise_orm.connections]
default = "@format {this.database_uri}"
qihaozhushou = "@format {this.qihaozhushou_database_uri}"
```

### 3. 本地覆盖 (.env)

```bash
# 开发环境完整配置（连接配置覆盖，应用配置与 default.toml 保持一致）
APP_TORTOISE_ORM='@json {"connections": {"default": "mysql://...", "qihaozhushou": "sqlite://..."}, "apps": {...}}'
```

**注意**: 由于 dynaconf 的限制，`.env` 中需要包含完整配置，但应用配置部分应与 `default.toml` 保持一致。

## 🔄 配置加载流程

1. **加载 default.toml** - 获取基础配置和应用配置
2. **加载环境特定文件** - 覆盖连接配置
3. **加载 .env 文件** - 本地开发覆盖（如果存在）
4. **加载环境变量** - 最高优先级覆盖

## 📝 配置维护指南

### 添加新模型

1. **只需修改 default.toml**:
```toml
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "models.new_module", "aerich.models"]
```

2. **同步更新 .env 文件**（如果使用）:
```bash
APP_TORTOISE_ORM='@json {"connections": {...}, "apps": {"models": {"models": ["models.douyin", "models.system", "models.trendinsight", "models.new_module", "aerich.models"], ...}}}'
```

### 添加新数据源

1. **修改 default.toml**:
```toml
[tortoise_orm.connections]
default = "sqlite://db.sqlite3"
qihaozhushou = "sqlite://qihaozhushou.sqlite3"
new_datasource = "sqlite://new_db.sqlite3"

[tortoise_orm.apps.new_app]
models = ["models.new_app"]
default_connection = "new_datasource"
```

2. **更新各环境文件**:
```toml
[tortoise_orm.connections]
default = "..."
qihaozhushou = "..."
new_datasource = "environment_specific_connection"
```

3. **更新 .env 文件**（如果使用）

### 修改环境连接

**只需修改对应环境的 TOML 文件**，无需修改 `default.toml`。

## ⚠️ 注意事项

### 1. 配置一致性

- `.env` 文件中的应用配置必须与 `default.toml` 保持一致
- 添加新模型时，需要同时更新 `default.toml` 和 `.env`

### 2. 环境隔离

- 开发环境: 使用 `.env` 覆盖
- 生产环境: 使用环境变量或 TOML 配置
- 不同环境的连接配置完全独立

### 3. 版本控制

- ✅ 提交: `default.toml`, `development.toml`, `staging.toml`, `production.toml`
- ❌ 不提交: `.env` (包含敏感信息)
- ✅ 提供: `.env.example` (示例配置)

## 🧪 配置验证

### 验证命令

```bash
# 检查当前配置
python scripts/check_config.py

# 验证多数据源配置
python scripts/validate_multi_database_config.py

# 测试使用示例
python examples/multi_database_usage.py
```

### 常见问题排查

1. **配置不生效**
   - 检查文件语法
   - 确认环境变量优先级
   - 重启应用

2. **应用配置缺失**
   - 检查 `default.toml` 中的应用配置
   - 确认 `.env` 中的应用配置与 `default.toml` 一致

3. **连接失败**
   - 检查对应环境的连接配置
   - 验证数据库服务状态

## 🎯 优势

1. **维护简单** - 应用配置只需在一个地方维护
2. **环境隔离** - 连接配置完全分离
3. **灵活覆盖** - 本地开发可灵活覆盖配置
4. **避免重复** - 减少配置重复和不一致风险

---

**设计版本**: v1.0  
**最后更新**: 2025-07-29  
**适用项目**: qihaozhushou-MediaCrawlerPro-Python
