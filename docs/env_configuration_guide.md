# .env 配置文件指南

## 📋 概述

`.env` 文件用于本地开发环境的配置覆盖，具有最高的配置优先级。它会覆盖 TOML 配置文件中的相应设置。

## 🔧 多数据源配置

### 当前 `.env` 配置

```bash
# 数据库配置 (使用 APP_ 前缀匹配 dynaconf)
APP_TORTOISE_ORM='@json {"connections": {"default": "mysql://qihaozhushou:qihaozhushou2025%40@47.101.186.209:3306/media_crawler", "qihaozhushou": "sqlite://qihaozhushou_dev.sqlite3"}, "apps": {"models": {"models": ["models.douyin", "models.system", "models.trendinsight", "aerich.models"], "default_connection": "default"}, "qihaozhushou": {"models": ["models.qihaozhushou"], "default_connection": "qihaozhushou"}}}'
```

### 配置解析

这个 JSON 配置包含两个主要部分：

#### 1. 数据库连接 (`connections`)

```json
{
  "connections": {
    "default": "mysql://qihaozhushou:qihaozhushou2025%40@47.101.186.209:3306/media_crawler",
    "qihaozhushou": "sqlite://qihaozhushou_dev.sqlite3"
  }
}
```

- **`default`**: 主数据库连接（MySQL 远程数据库）
  - 用于抖音、系统、趋势洞察等主要业务模型
- **`qihaozhushou`**: 奇好助手专用数据库（SQLite 本地文件）
  - 用于 `UserInboxSourceRelated` 和 `UserInboxVideoRelated` 模型

#### 2. 应用配置 (`apps`)

```json
{
  "apps": {
    "models": {
      "models": ["models.douyin", "models.system", "models.trendinsight", "aerich.models"],
      "default_connection": "default"
    },
    "qihaozhushou": {
      "models": ["models.qihaozhushou"],
      "default_connection": "qihaozhushou"
    }
  }
}
```

- **`models`** 应用: 包含主要业务模型，使用 `default` 连接
- **`qihaozhushou`** 应用: 包含奇好助手模型，使用 `qihaozhushou` 连接

## 🔄 配置优先级

1. **`.env` 文件** (最高优先级)
2. **环境变量**
3. **`settings/{environment}.toml`** (如 `development.toml`)
4. **`settings/default.toml`** (最低优先级)

## 📝 配置修改指南

### 修改数据库连接

如果需要修改数据库连接，更新 `.env` 文件中的相应部分：

```bash
# 示例：修改为不同的数据库
APP_TORTOISE_ORM='@json {"connections": {"default": "sqlite://main.sqlite3", "qihaozhushou": "sqlite://qihaozhushou.sqlite3"}, "apps": {"models": {"models": ["models.douyin", "models.system", "models.trendinsight", "aerich.models"], "default_connection": "default"}, "qihaozhushou": {"models": ["models.qihaozhushou"], "default_connection": "qihaozhushou"}}}'
```

### 添加新的数据源

如果需要添加新的数据源，需要同时更新：

1. **连接配置**:
```json
{
  "connections": {
    "default": "...",
    "qihaozhushou": "...",
    "new_datasource": "sqlite://new_db.sqlite3"
  }
}
```

2. **应用配置**:
```json
{
  "apps": {
    "models": {...},
    "qihaozhushou": {...},
    "new_app": {
      "models": ["models.new_module"],
      "default_connection": "new_datasource"
    }
  }
}
```

## ⚠️ 重要注意事项

### 1. JSON 格式要求

- 必须是有效的 JSON 格式
- 字符串必须使用双引号
- 特殊字符需要正确转义（如 `%40` 代表 `@`）

### 2. 与 TOML 配置的一致性

确保 `.env` 文件的配置与对应的 TOML 配置文件保持一致，避免配置冲突。

### 3. 安全考虑

- `.env` 文件包含敏感信息（数据库密码等）
- 确保 `.env` 文件已添加到 `.gitignore`
- 不要将 `.env` 文件提交到版本控制系统

### 4. 环境隔离

- 开发环境使用 `.env` 文件
- 生产环境使用环境变量或配置管理系统
- 不同环境使用不同的数据库连接

## 🧪 配置验证

使用以下命令验证配置是否正确：

```bash
# 检查配置加载
python scripts/check_config.py

# 验证多数据源配置
python scripts/validate_multi_database_config.py

# 运行使用示例
python examples/multi_database_usage.py
```

## 🔧 故障排除

### 常见问题

1. **配置不生效**
   - 检查 JSON 格式是否正确
   - 确认环境变量名称是否正确（`APP_TORTOISE_ORM`）
   - 重启应用程序

2. **数据库连接失败**
   - 检查数据库 URI 格式
   - 确认数据库服务是否运行
   - 验证用户名和密码

3. **模型导入错误**
   - 检查模型路径是否正确
   - 确认模型文件是否存在
   - 验证应用配置

### 调试命令

```bash
# 查看当前配置
python -c "from settings.config import settings; print(settings.TORTOISE_ORM)"

# 检查环境变量
python -c "import os; print('APP_TORTOISE_ORM' in os.environ)"

# 测试数据库连接
python scripts/validate_multi_database_config.py
```

## 📚 相关文档

- [多数据源配置指南](multi_database_setup.md)
- [配置完成总结](multi_database_summary.md)
- [使用示例](../examples/multi_database_usage.py)

---

**最后更新**: 2025-07-29  
**配置版本**: v1.0  
**适用环境**: 开发环境
