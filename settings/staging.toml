# 预发布环境配置
[staging]
database_uri = "sqlite:///./test.db"
log_level = "DEBUG"
debug = true
secret_key = "staging-secret-key-for-staging-only"
kuaidaili_dps_order_id = "your-staging-order-id-here"

# Tortoise ORM 配置 - 预发布环境多数据源
[tortoise_orm]
[tortoise_orm.connections]
default = "sqlite://staging.sqlite3"
qihaozhushou = "mysql://qihaozhushou:qihaozhushou2025%40@172.19.73.245:3306/qihaozhushou_staging"  # 奇好助手预发布数据库

[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"

# 预发布环境特定配置
app_title = "Vue FastAPI Admin - Staging"
project_name = "Vue FastAPI Admin - Staging"

# 预发布环境CORS设置
cors_origins = ["http://localhost:3000", "http://localhost:8080"]

# 预发布环境JWT过期时间
jwt_access_token_expire_minutes = 60  # 1 hour

# 预发布环境特定的快代理配置
kuaidaili_api_base_url = "https://dps.kdlapi.com"