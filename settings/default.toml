# 默认配置
[default]
database_uri = "sqlite:///./test.db"
log_level = "INFO"
debug = true

# Tortoise ORM 配置 - 多数据源
[tortoise_orm]

# 默认连接配置（各环境会覆盖这些设置）
[tortoise_orm.connections]
default = "sqlite://db.sqlite3"
qihaozhushou = "sqlite://qihaozhushou.sqlite3"  # 奇好助手专用数据库

# 应用配置（所有环境共享，不区分测试/正式环境）
[tortoise_orm.apps]
[tortoise_orm.apps.models]
models = ["models.douyin", "models.system", "models.trendinsight", "aerich.models"]
default_connection = "default"

[tortoise_orm.apps.qihaozhushou]
models = ["models.qihaozhushou"]
default_connection = "qihaozhushou"

# 应用配置
secret_key = "your-secret-key-here-change-in-production"
kuaidaili_dps_secret_id = "your-secret-id-here"
kuaidaili_dps_signature = "your-signature-here"

# 快代理配置
# kuaidaili_dps_secret_id = "your_secret_id_here"
# kuaidaili_dps_signature = "your_signature_here"

# TrendInsight 配置
# trendinsight_base_url = "https://trendinsight.oceanengine.com"
# trendinsight_timeout = 30.0
# trendinsight_max_retries = 3
# trendinsight_retry_delay = 1.0
# trendinsight_verify_ssl = true
# trendinsight_use_js_sign = true
# trendinsight_js_file_path = "/path/to/sign.js"
# trendinsight_sign_cache_enabled = true
# trendinsight_sign_cache_ttl = 300

# 应用基本信息
version = "0.1.0"
app_title = "Vue FastAPI Admin"
project_name = "Vue FastAPI Admin"
app_description = "Description"

# CORS 设置
cors_origins = ["*"]
cors_allow_credentials = true
cors_allow_methods = ["*"]
cors_allow_headers = ["*"]

# JWT 设置
jwt_algorithm = "HS256"
jwt_access_token_expire_minutes = 10080  # 7 days

# 日期时间格式
datetime_format = "%Y-%m-%d %H:%M:%S"

# 快代理设置
kuaidaili_api_base_url = "https://dps.kdlapi.com"
kuaidaili_username = "your-username"
kuaidaili_password = "your-password"

# 代理池配置
proxy_pool_default_count = 2  # 默认获取的代理数量
proxy_pool_cache_ttl = 3600   # 代理缓存时间（秒）