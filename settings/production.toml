# 生产环境配置
[production]
database_uri = "env_var_for_prod_db_uri"
log_level = "WARNING"
debug = false
secret_key = "prod-secret-key-change-this"
kuaidaili_dps_order_id = "your-prod-order-id-here"

# Tortoise ORM 配置 - 生产环境多数据源
qihaozhushou_database_uri = "mysql://qihaozhushou:qihaozhushou2025%40@47.101.186.209:3306/qihaozhushou_prod"

# 注意：应用配置在 default.toml 中统一定义，这里只配置连接
[tortoise_orm]
[tortoise_orm.connections]
default = "@format {this.database_uri}"
qihaozhushou = "@format {this.qihaozhushou_database_uri}"

# 生产环境特定配置
app_title = "Vue FastAPI Admin"
project_name = "Vue FastAPI Admin"

# 生产环境严格的CORS设置
cors_origins = ["https://yourdomain.com", "https://www.yourdomain.com"]

# 生产环境JWT过期时间
jwt_access_token_expire_minutes = 1440  # 24 hours