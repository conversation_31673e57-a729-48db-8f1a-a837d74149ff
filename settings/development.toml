# 开发环境配置
[development]
database_uri = "postgresql://user:pass@localhost:5432/devdb"
log_level = "DEBUG"
debug = true
secret_key = "dev-secret-key-change-in-production"
kuaidaili_dps_order_id = "your-dev-order-id-here"

# Tortoise ORM 配置 - 开发环境多数据源
# 注意：应用配置在 default.toml 中统一定义，这里只配置连接
[tortoise_orm]
[tortoise_orm.connections]
default = "sqlite://db.sqlite3"  # 开发环境默认使用 SQLite，可通过 .env 覆盖
qihaozhushou = "sqlite://qihaozhushou_dev.sqlite3"  # 奇好助手开发环境数据库

# 开发环境特定配置
app_title = "Vue FastAPI Admin - Development"
project_name = "Vue FastAPI Admin - Dev"

# 开发环境允许更宽松的CORS设置
cors_origins = ["http://localhost:3000", "http://localhost:8080", "http://127.0.0.1:3000", "http://127.0.0.1:8080"]

# 开发环境JWT过期时间更长
jwt_access_token_expire_minutes = 43200  # 30 days