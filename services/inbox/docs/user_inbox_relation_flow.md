# 用户收件箱关联处理逻辑文档

## 一、业务流程说明

### 1. user_inbox_source_related 表
- 传入 source_id、source_type、user_uuid
- 查询 user_inbox_source_related 是否存在有效记录（未删除）
  - 若无，则新建一条记录
  - 若有，则跳过创建，直接进入下一步

### 2. user_inbox_video_related 表
- 以某一行 user_inbox_source_related 记录为基础，获取其关联的所有视频列表（[{ aweme_id, publish_time }]）
- 批量检查 user_inbox_video_related 是否已存在对应记录（user_uuid, video_id, source_type, is_deleted=False）
  - 若无，则新建记录
  - 若有，则跳过

---

## 二、流程图

```mermaid
flowchart TD
    A[开始] --> B{user_inbox_source_related是否存在?}
    B -- 否 --> C[新建user_inbox_source_related]
    B -- 是 --> D[获取视频列表]
    C --> D
    D --> E{user_inbox_video_related是否存在?}
    E -- 否 --> F[新建user_inbox_video_related]
    E -- 是 --> G[跳过]
    F --> H[下一个视频]
    G --> H
    H --> I{是否还有视频?}
    I -- 是 --> D
    I -- 否 --> J[结束]
```

---

## 三、时序图

```mermaid
sequenceDiagram
    participant Client
    participant InboxService
    participant DB

    Client->>InboxService: 提交 source_id, source_type, user_uuid
    InboxService->>DB: 查询 user_inbox_source_related
    alt 未找到
        InboxService->>DB: 新建 user_inbox_source_related
    end
    InboxService->>DB: 查询视频列表
    loop 视频列表
        InboxService->>DB: 查询 user_inbox_video_related
        alt 未找到
            InboxService->>DB: 新建 user_inbox_video_related
        end
    end
```

---

## 四、UML 类图

```mermaid
classDiagram
    class UserInboxSourceRelated {
        +uuid: str
        +user_uuid: str
        +source_id: str
        +source_type: str
        +create_time: datetime
        +update_time: datetime
        +is_deleted: bool
        +deleted_at: str
    }

    class UserInboxVideoRelated {
        +uuid: str
        +user_uuid: str
        +video_id: str
        +source_type: str
        +create_time: datetime
        +update_time: datetime
        +is_deleted: bool
        +deleted_at: str
    }

    UserInboxSourceRelated "1" -- "0..*" UserInboxVideoRelated : 关联
```

---

## 五、代码处理建议

- 优先用 ORM 查询和批量插入，减少字符串拼接
- 关联逻辑建议封装为 service 层方法，便于复用和测试
- 批量处理时注意事务和异常捕获，保证数据一致性

---

## 六、代码实现

### 1. 服务架构

```
services/inbox/
├── __init__.py                 # 模块导出
├── inbox_service.py           # 主服务入口
├── inbox_relation_service.py  # 关联关系处理
├── video_data_processor.py    # 视频数据处理
├── examples.py               # 使用示例
├── tests.py                  # 单元测试
└── docs/
    └── user_inbox_relation_flow.md  # 本文档
```

### 2. 核心服务类

#### InboxService（主服务）
- `process_source_videos()` - 处理来源视频的完整流程
- `batch_process_multiple_sources()` - 批量处理多个来源
- `get_user_inbox_summary()` - 获取用户收件箱摘要
- `cleanup_user_relations()` - 清理用户关联关系

#### InboxRelationService（关联处理）
- `process_user_inbox_relation()` - 核心关联处理逻辑
- `_ensure_source_relation()` - 确保来源关联存在
- `_process_video_relations()` - 批量处理视频关联
- `get_user_source_relations()` - 获取用户来源关联
- `get_user_video_relations()` - 获取用户视频关联

#### VideoDataProcessor（数据处理）
- `format_video_list()` - 格式化视频列表
- `deduplicate_videos()` - 视频去重
- `filter_videos_by_time_range()` - 时间范围过滤
- `batch_process_videos()` - 视频分批处理

### 3. 使用示例

```python
from services.inbox import InboxService

# 初始化服务
inbox_service = InboxService()

# 处理来源视频
result = await inbox_service.process_source_videos(
    user_uuid="user123456789",
    source_id="author_abc123",
    source_type="author",
    raw_video_list=[
        {"aweme_id": "7123456789012345678", "publish_time": 1640995200},
        {"aweme_id": "7123456789012345679", "publish_time": "2022-01-01 12:00:00"}
    ],
    enable_deduplication=True
)

# 获取用户收件箱摘要
summary = await inbox_service.get_user_inbox_summary("user123456789")
```

### 4. 特性支持

- ✅ 自动去重处理
- ✅ 时间范围过滤
- ✅ 批量处理优化
- ✅ 错误处理和日志记录
- ✅ 性能监控
- ✅ 参数验证
- ✅ 软删除支持
- ✅ 事务安全

---

如需进一步细化某一环节或补充具体代码实现，可随时告知！
