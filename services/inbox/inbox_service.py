"""
收件箱服务 - 统一的收件箱关联处理入口
"""

from datetime import datetime
from typing import Dict, List, Optional

from services.base import BaseService, ValidationError
from .inbox_relation_service import InboxRelationService
from .video_data_processor import VideoDataProcessor
from models.qihaozhushou import UserInboxSourceRelated
from models.douyin import DouyinAweme


class InboxService(BaseService):
    """收件箱服务 - 统一的收件箱关联处理入口"""

    def __init__(self, logger=None):
        super().__init__(logger)
        self.relation_service = InboxRelationService(logger)
        self.video_processor = VideoDataProcessor(logger)

    async def process_source_videos(
        self,
        user_inbox_source_related: UserInboxSourceRelated,
        raw_video_list: List[DouyinAweme],
        enable_deduplication: bool = True,
        enable_time_filter: bool = False,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, any]:
        """
        处理来源视频的完整流程

        Args:
            user_inbox_source_related: 用户收件箱来源关联对象
            raw_video_list: DouyinAweme 对象列表
            enable_deduplication: 是否启用去重
            enable_time_filter: 是否启用时间过滤
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）

        Returns:
            Dict[str, any]: 处理结果
        """
        # 参数验证
        self.validate_required_params(
            user_inbox_source_related=user_inbox_source_related
        )

        # 从对象中提取字段
        user_uuid = user_inbox_source_related.user_uuid
        source_id = user_inbox_source_related.source_id
        source_type = user_inbox_source_related.source_type

        if not self.video_processor.validate_source_data(source_id, source_type):
            raise ValidationError("无效的来源数据")

        operation_id = self.performance_monitor.start_operation("process_source_videos")
        
        try:
            self.service_logger.log_business_event(
                "开始处理来源视频",
                {
                    "user_uuid": user_uuid,
                    "source_id": source_id,
                    "source_type": source_type,
                    "raw_video_count": len(raw_video_list),
                    "enable_deduplication": enable_deduplication,
                    "enable_time_filter": enable_time_filter
                }
            )

            # 1. 格式化视频数据
            formatted_videos = self.video_processor.format_video_list(raw_video_list)
            
            if not formatted_videos:
                return {
                    "success": True,
                    "message": "无有效视频数据需要处理",
                    "statistics": self.video_processor.create_processing_summary(
                        len(raw_video_list), 0, len(raw_video_list)
                    )
                }

            # 2. 去重处理（可选）
            if enable_deduplication:
                formatted_videos = self.video_processor.deduplicate_videos(formatted_videos)

            # 3. 时间过滤（可选）
            if enable_time_filter and (start_time or end_time):
                formatted_videos = self.video_processor.filter_videos_by_time_range(
                    formatted_videos, start_time, end_time
                )

            # 4. 处理收件箱关联
            relation_result = await self.relation_service.process_user_inbox_relation(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type,
                video_list=formatted_videos
            )

            # 5. 生成处理摘要
            processing_summary = self.video_processor.create_processing_summary(
                len(raw_video_list),
                relation_result["video_relations_created"]
            )

            result = {
                "success": True,
                "message": "来源视频处理完成",
                "relation_result": relation_result,
                "processing_summary": processing_summary,
                "video_count": {
                    "original": len(raw_video_list),
                    "formatted": len(formatted_videos),
                    "processed": relation_result["total_videos_processed"]
                }
            }

            self.service_logger.log_business_event(
                "来源视频处理完成",
                result
            )

            return result

        except Exception as e:
            self.error_tracker.track_error("process_source_videos", e)
            raise
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def batch_process_multiple_sources(
        self,
        user_uuid: str,
        source_data_list: List[Dict],
        batch_size: int = 10
    ) -> Dict[str, any]:
        """
        批量处理多个来源的视频

        Args:
            user_uuid: 用户UUID
            source_data_list: 来源数据列表 [{"source_id": "xxx", "source_type": "xxx", "videos": [DouyinAweme, ...]}]
            batch_size: 批处理大小

        Returns:
            Dict[str, any]: 批处理结果
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        if not source_data_list:
            return {
                "success": True,
                "message": "无来源数据需要处理",
                "results": []
            }

        operation_id = self.performance_monitor.start_operation("batch_process_multiple_sources")
        
        try:
            self.service_logger.log_business_event(
                "开始批量处理多个来源",
                {
                    "user_uuid": user_uuid,
                    "source_count": len(source_data_list),
                    "batch_size": batch_size
                }
            )

            results = []
            total_processed = 0
            total_errors = 0

            # 分批处理
            for i in range(0, len(source_data_list), batch_size):
                batch = source_data_list[i:i + batch_size]
                
                for source_data in batch:
                    try:
                        source_id = source_data.get("source_id")
                        source_type = source_data.get("source_type")
                        videos = source_data.get("videos", [])

                        if not source_id or not source_type:
                            total_errors += 1
                            results.append({
                                "source_id": source_id,
                                "source_type": source_type,
                                "success": False,
                                "error": "缺少必要的来源信息"
                            })
                            continue

                        # 获取或创建 user_inbox_source_related 对象
                        user_inbox_source_related = await self.get_or_create_source_relation(
                            user_uuid, source_id, source_type
                        )

                        # 处理单个来源
                        result = await self.process_source_videos(
                            user_inbox_source_related=user_inbox_source_related,
                            raw_video_list=videos
                        )
                        
                        results.append({
                            "source_id": source_id,
                            "source_type": source_type,
                            "success": True,
                            "result": result
                        })
                        total_processed += 1

                    except Exception as e:
                        total_errors += 1
                        results.append({
                            "source_id": source_data.get("source_id"),
                            "source_type": source_data.get("source_type"),
                            "success": False,
                            "error": str(e)
                        })

            batch_result = {
                "success": True,
                "message": f"批量处理完成，成功: {total_processed}, 失败: {total_errors}",
                "statistics": {
                    "total_sources": len(source_data_list),
                    "processed_sources": total_processed,
                    "error_sources": total_errors,
                    "success_rate": round((total_processed / len(source_data_list) * 100), 2)
                },
                "results": results
            }

            self.service_logger.log_business_event(
                "批量处理多个来源完成",
                batch_result["statistics"]
            )

            return batch_result

        except Exception as e:
            self.error_tracker.track_error("batch_process_multiple_sources", e)
            raise
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def get_user_inbox_summary(self, user_uuid: str) -> Dict[str, any]:
        """
        获取用户收件箱摘要信息
        
        Args:
            user_uuid: 用户UUID
            
        Returns:
            Dict[str, any]: 收件箱摘要
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        try:
            # 获取来源关联
            source_relations = await self.relation_service.get_user_source_relations(user_uuid)
            
            # 获取视频关联
            video_relations = await self.relation_service.get_user_video_relations(user_uuid)
            
            # 按来源类型统计
            source_stats = {}
            for relation in source_relations:
                source_type = relation.source_type
                if source_type not in source_stats:
                    source_stats[source_type] = 0
                source_stats[source_type] += 1

            video_stats = {}
            for relation in video_relations:
                source_type = relation.source_type
                if source_type not in video_stats:
                    video_stats[source_type] = 0
                video_stats[source_type] += 1

            summary = {
                "user_uuid": user_uuid,
                "source_relations": {
                    "total": len(source_relations),
                    "by_type": source_stats
                },
                "video_relations": {
                    "total": len(video_relations),
                    "by_type": video_stats
                },
                "last_updated": datetime.now().isoformat()
            }

            return summary

        except Exception as e:
            raise ValidationError(f"获取用户收件箱摘要失败: {str(e)}")

    async def cleanup_user_relations(
        self,
        user_inbox_source_related: UserInboxSourceRelated
    ) -> Dict[str, bool]:
        """
        清理用户关联关系

        Args:
            user_inbox_source_related: 用户收件箱来源关联对象

        Returns:
            Dict[str, bool]: 清理结果
        """
        self.validate_required_params(
            user_inbox_source_related=user_inbox_source_related
        )

        # 从对象中提取字段
        user_uuid = user_inbox_source_related.user_uuid
        source_id = user_inbox_source_related.source_id
        source_type = user_inbox_source_related.source_type
        
        try:
            # 删除来源关联
            source_deleted = await self.relation_service.remove_source_relation(
                user_uuid, source_id, source_type
            )

            self.service_logger.log_business_event(
                "用户关联关系清理完成",
                {
                    "user_uuid": user_uuid,
                    "source_id": source_id,
                    "source_type": source_type,
                    "source_deleted": source_deleted
                }
            )

            return {
                "source_deleted": source_deleted
            }

        except Exception as e:
            raise ValidationError(f"清理用户关联关系失败: {str(e)}")

    async def get_or_create_source_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str
    ) -> UserInboxSourceRelated:
        """
        获取或创建用户收件箱来源关联对象

        Args:
            user_uuid: 用户UUID
            source_id: 来源ID
            source_type: 来源类型

        Returns:
            UserInboxSourceRelated: 用户收件箱来源关联对象
        """
        self.validate_required_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type
        )

        try:
            # 查询是否已存在有效记录
            existing = await UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type,
                is_deleted=False
            ).first()

            if existing:
                return existing

            # 创建新的对象（但不保存到数据库）
            return UserInboxSourceRelated(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type
            )

        except Exception as e:
            raise ValidationError(f"获取或创建来源关联对象失败: {str(e)}")

    async def process_source_videos_by_params(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str,
        raw_video_list: List[DouyinAweme],
        enable_deduplication: bool = True,
        enable_time_filter: bool = False,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> Dict[str, any]:
        """
        通过参数处理来源视频（兼容性方法）

        Args:
            user_uuid: 用户UUID
            source_id: 来源ID
            source_type: 来源类型
            raw_video_list: DouyinAweme 对象列表
            enable_deduplication: 是否启用去重
            enable_time_filter: 是否启用时间过滤
            start_time: 开始时间（可选）
            end_time: 结束时间（可选）

        Returns:
            Dict[str, any]: 处理结果
        """
        # 获取或创建 user_inbox_source_related 对象
        user_inbox_source_related = await self.get_or_create_source_relation(
            user_uuid, source_id, source_type
        )

        # 调用主要的处理方法
        return await self.process_source_videos(
            user_inbox_source_related=user_inbox_source_related,
            raw_video_list=raw_video_list,
            enable_deduplication=enable_deduplication,
            enable_time_filter=enable_time_filter,
            start_time=start_time,
            end_time=end_time
        )
