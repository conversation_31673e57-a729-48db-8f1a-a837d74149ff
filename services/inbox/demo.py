#!/usr/bin/env python3
"""
收件箱服务演示脚本
"""

import asyncio
import logging
import sys
import os
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from services.inbox import InboxService, VideoDataProcessor


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)8s] %(name)s: %(message)s'
)
logger = logging.getLogger(__name__)


async def demo_video_data_processing():
    """演示视频数据处理功能"""
    print("\n" + "="*60)
    print("📹 视频数据处理演示")
    print("="*60)
    
    processor = VideoDataProcessor(logger)
    
    # 模拟原始视频数据
    raw_videos = [
        {
            "aweme_id": "7123456789012345678",
            "publish_time": 1640995200,  # 时间戳
            "title": "测试视频1",
            "author": "测试作者1"
        },
        {
            "aweme_id": "7123456789012345679", 
            "publish_time": "2022-01-01 12:00:00",  # 字符串格式
            "title": "测试视频2",
            "author": "测试作者2"
        },
        {
            "aweme_id": "7123456789012345678",  # 重复ID
            "publish_time": datetime.now(),  # datetime对象
            "title": "重复视频1",
            "author": "测试作者1"
        },
        {
            "aweme_id": "",  # 无效ID
            "publish_time": 1640995300,
            "title": "无效视频",
            "author": "测试作者3"
        }
    ]
    
    print(f"📊 原始视频数据: {len(raw_videos)} 条")
    
    # 1. 格式化视频数据
    formatted_videos = processor.format_video_list(raw_videos)
    print(f"✅ 格式化后: {len(formatted_videos)} 条有效视频")
    
    # 2. 去重处理
    deduplicated_videos = processor.deduplicate_videos(formatted_videos)
    print(f"🔄 去重后: {len(deduplicated_videos)} 条唯一视频")
    
    # 3. 提取视频ID
    video_ids = processor.extract_video_ids(deduplicated_videos)
    print(f"🆔 视频ID列表: {video_ids}")
    
    # 4. 分批处理
    batches = processor.batch_process_videos(deduplicated_videos, batch_size=2)
    print(f"📦 分批结果: {len(batches)} 批，每批最多2个视频")
    
    # 5. 生成处理摘要
    summary = processor.create_processing_summary(
        len(raw_videos), len(deduplicated_videos), 1
    )
    print(f"📈 处理摘要: {summary}")
    
    return deduplicated_videos


async def demo_basic_service_usage():
    """演示基础服务使用"""
    print("\n" + "="*60)
    print("🏠 基础服务使用演示")
    print("="*60)
    
    # 注意：这个演示不会实际连接数据库
    # 在实际使用中需要确保数据库连接已配置
    
    inbox_service = InboxService(logger)
    
    # 模拟用户和来源数据
    user_uuid = "demo_user_123456789"
    source_id = "demo_author_abc123"
    source_type = "author"
    
    # 模拟视频数据
    raw_video_list = [
        {
            "aweme_id": "7123456789012345678",
            "publish_time": int((datetime.now() - timedelta(days=1)).timestamp()),
            "title": "演示视频1"
        },
        {
            "aweme_id": "7123456789012345679",
            "publish_time": int((datetime.now() - timedelta(hours=12)).timestamp()),
            "title": "演示视频2"
        },
        {
            "aweme_id": "7123456789012345680",
            "publish_time": int(datetime.now().timestamp()),
            "title": "演示视频3"
        }
    ]
    
    print(f"👤 用户UUID: {user_uuid}")
    print(f"📺 来源ID: {source_id}")
    print(f"🏷️  来源类型: {source_type}")
    print(f"📹 视频数量: {len(raw_video_list)}")
    
    try:
        # 这里会因为没有数据库连接而失败，但可以看到参数验证等逻辑
        print("\n🔄 开始处理（注意：需要数据库连接）...")
        
        # 实际调用会失败，但我们可以展示参数验证
        print("✅ 参数验证通过")
        print("✅ 来源数据验证通过")
        print("✅ 视频数据格式化完成")
        
        # 模拟处理结果
        mock_result = {
            "success": True,
            "message": "来源视频处理完成",
            "relation_result": {
                "source_relations_created": 1,
                "video_relations_created": 3,
                "total_videos_processed": 3
            },
            "processing_summary": {
                "original_count": 3,
                "processed_count": 3,
                "error_count": 0,
                "success_rate": 100.0
            },
            "video_count": {
                "original": 3,
                "formatted": 3,
                "processed": 3
            }
        }
        
        print(f"📊 模拟处理结果: {mock_result}")
        
    except Exception as e:
        print(f"⚠️  预期错误（需要数据库连接）: {type(e).__name__}")


async def demo_batch_processing():
    """演示批量处理功能"""
    print("\n" + "="*60)
    print("📦 批量处理演示")
    print("="*60)
    
    inbox_service = InboxService(logger)
    user_uuid = "demo_user_123456789"
    
    # 模拟多个来源的数据
    source_data_list = [
        {
            "source_id": "author_001",
            "source_type": "author",
            "videos": [
                {"aweme_id": "7001", "publish_time": 1640995200},
                {"aweme_id": "7002", "publish_time": 1640995300}
            ]
        },
        {
            "source_id": "author_002",
            "source_type": "author", 
            "videos": [
                {"aweme_id": "7003", "publish_time": 1640995400},
                {"aweme_id": "7004", "publish_time": 1640995500}
            ]
        },
        {
            "source_id": "keyword_001",
            "source_type": "keyword",
            "videos": [
                {"aweme_id": "7005", "publish_time": 1640995600}
            ]
        }
    ]
    
    print(f"👤 用户UUID: {user_uuid}")
    print(f"📊 来源数量: {len(source_data_list)}")
    
    total_videos = sum(len(source["videos"]) for source in source_data_list)
    print(f"📹 总视频数量: {total_videos}")
    
    # 展示批量处理逻辑（不实际执行数据库操作）
    print("\n🔄 批量处理逻辑演示:")
    for i, source_data in enumerate(source_data_list, 1):
        source_id = source_data["source_id"]
        source_type = source_data["source_type"]
        video_count = len(source_data["videos"])
        
        print(f"  {i}. 处理来源 {source_id} ({source_type}) - {video_count} 个视频")
        print(f"     ✅ 参数验证通过")
        print(f"     ✅ 视频数据格式化完成")
        print(f"     ✅ 关联关系处理完成")
    
    # 模拟批量处理结果
    mock_batch_result = {
        "success": True,
        "message": f"批量处理完成，成功: {len(source_data_list)}, 失败: 0",
        "statistics": {
            "total_sources": len(source_data_list),
            "processed_sources": len(source_data_list),
            "error_sources": 0,
            "success_rate": 100.0
        }
    }
    
    print(f"\n📊 模拟批量处理结果: {mock_batch_result}")


async def demo_time_filtering():
    """演示时间过滤功能"""
    print("\n" + "="*60)
    print("⏰ 时间过滤演示")
    print("="*60)
    
    processor = VideoDataProcessor(logger)
    
    # 模拟不同时间的视频数据
    now = datetime.now()
    videos_with_time = [
        {
            "aweme_id": "7201",
            "publish_time": (now - timedelta(days=7)).strftime("%Y-%m-%d %H:%M:%S"),
            "title": "7天前的视频"
        },
        {
            "aweme_id": "7202",
            "publish_time": (now - timedelta(days=3)).strftime("%Y-%m-%d %H:%M:%S"),
            "title": "3天前的视频"
        },
        {
            "aweme_id": "7203",
            "publish_time": (now - timedelta(days=1)).strftime("%Y-%m-%d %H:%M:%S"),
            "title": "1天前的视频"
        },
        {
            "aweme_id": "7204",
            "publish_time": now.strftime("%Y-%m-%d %H:%M:%S"),
            "title": "刚刚发布的视频"
        }
    ]
    
    print(f"📹 原始视频: {len(videos_with_time)} 条")
    for video in videos_with_time:
        print(f"  - {video['aweme_id']}: {video['title']} ({video['publish_time']})")
    
    # 过滤最近3天的视频
    start_time = now - timedelta(days=3)
    print(f"\n🔍 过滤条件: 最近3天 (从 {start_time.strftime('%Y-%m-%d %H:%M:%S')} 开始)")
    
    filtered_videos = processor.filter_videos_by_time_range(
        videos_with_time, start_time=start_time
    )
    
    print(f"✅ 过滤后视频: {len(filtered_videos)} 条")
    for video in filtered_videos:
        print(f"  - {video['aweme_id']}: {video['title']} ({video['publish_time']})")


async def main():
    """主演示函数"""
    print("🎬 收件箱服务演示")
    print("=" * 80)
    print("本演示展示收件箱服务的主要功能和使用方法")
    print("注意：实际数据库操作需要正确配置数据库连接")
    print("=" * 80)
    
    try:
        # 1. 视频数据处理演示
        processed_videos = await demo_video_data_processing()
        
        # 2. 基础服务使用演示
        await demo_basic_service_usage()
        
        # 3. 批量处理演示
        await demo_batch_processing()
        
        # 4. 时间过滤演示
        await demo_time_filtering()
        
        print("\n" + "="*60)
        print("🎉 演示完成！")
        print("="*60)
        print("📚 更多信息请查看:")
        print("  - README.md: 详细使用说明")
        print("  - examples.py: 完整使用示例")
        print("  - tests.py: 单元测试")
        print("  - docs/user_inbox_relation_flow.md: 业务流程文档")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        logger.exception("演示执行失败")


if __name__ == "__main__":
    asyncio.run(main())
