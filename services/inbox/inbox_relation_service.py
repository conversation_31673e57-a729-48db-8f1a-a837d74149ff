"""
用户收件箱关联处理服务 - 实现文档中定义的业务逻辑
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from models.qihaozhushou import UserInboxSourceRelated, UserInboxVideoRelated
from services.base import BaseService, DatabaseError, ValidationError
from services.base.database_optimizer import DatabaseOptimizer


class InboxRelationService(BaseService):
    """用户收件箱关联处理服务"""

    def __init__(self, logger=None):
        super().__init__(logger)
        self.db_optimizer = DatabaseOptimizer(self.service_logger)

    def _generate_uuid(self) -> str:
        """生成32位UUID（去除连字符）"""
        return str(uuid.uuid4()).replace('-', '')

    async def process_user_inbox_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str,
        video_list: List[Dict[str, str]]
    ) -> Dict[str, int]:
        """
        处理用户收件箱关联逻辑
        
        Args:
            user_uuid: 用户UUID
            source_id: 来源ID
            source_type: 来源类型（video/author）
            video_list: 视频列表 [{"aweme_id": "xxx", "publish_time": "xxx"}]
            
        Returns:
            Dict[str, int]: 处理结果统计
        """
        # 参数验证
        self.validate_required_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type
        )
        
        if source_type not in ["video", "author"]:
            raise ValidationError(f"不支持的来源类型: {source_type}")

        operation_id = self.performance_monitor.start_operation("process_user_inbox_relation")
        
        try:
            # 记录业务事件
            self.service_logger.log_business_event(
                "开始处理用户收件箱关联",
                {
                    "user_uuid": user_uuid,
                    "source_id": source_id,
                    "source_type": source_type,
                    "video_count": len(video_list)
                }
            )

            # 1. 处理 user_inbox_source_related 表
            source_relation_created = await self._ensure_source_relation(
                user_uuid, source_id, source_type
            )

            # 2. 处理 user_inbox_video_related 表
            video_relations_created = await self._process_video_relations(
                user_uuid, source_type, video_list
            )

            result = {
                "source_relations_created": source_relation_created,
                "video_relations_created": video_relations_created,
                "total_videos_processed": len(video_list)
            }

            self.service_logger.log_business_event(
                "用户收件箱关联处理完成",
                result
            )

            return result

        except Exception as e:
            self.error_tracker.track_error("process_user_inbox_relation", e)
            raise DatabaseError(f"处理用户收件箱关联失败: {str(e)}")
        finally:
            self.performance_monitor.finish_operation(operation_id)

    async def _ensure_source_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str
    ) -> int:
        """
        确保 user_inbox_source_related 记录存在
        
        Returns:
            int: 创建的记录数量（0或1）
        """
        try:
            # 查询是否已存在有效记录
            existing = await UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type,
                is_deleted=False
            ).first()

            if existing:
                self.service_logger.log_business_event(
                    "跳过来源关联创建",
                    {"reason": "记录已存在", "existing_uuid": existing.uuid}
                )
                return 0

            # 创建新记录
            new_relation = await UserInboxSourceRelated.create(
                uuid=self._generate_uuid(),
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type,
                is_deleted=False,
                deleted_at=""
            )

            self.service_logger.log_business_event(
                "来源关联创建成功",
                {"new_uuid": new_relation.uuid}
            )

            return 1

        except Exception as e:
            raise DatabaseError(f"处理来源关联失败: {str(e)}")

    async def _process_video_relations(
        self,
        user_uuid: str,
        source_type: str,
        video_list: List[Dict[str, str]]
    ) -> int:
        """
        批量处理视频关联记录
        
        Returns:
            int: 创建的记录数量
        """
        if not video_list:
            return 0

        try:
            # 提取视频ID列表
            video_ids = [video.get("aweme_id") for video in video_list if video.get("aweme_id")]
            
            if not video_ids:
                self.service_logger.log_business_event(
                    "跳过视频关联处理",
                    {"reason": "无有效视频ID"}
                )
                return 0

            # 批量检查已存在的记录
            existing_video_ids = await self.db_optimizer.batch_exists_check(
                model=UserInboxVideoRelated,
                field_name="video_id",
                values=video_ids,
                additional_filters={
                    "user_uuid": user_uuid,
                    "source_type": source_type,
                    "is_deleted": False
                }
            )

            # 计算需要创建的新记录
            new_video_ids = [vid for vid in video_ids if vid not in existing_video_ids]

            if not new_video_ids:
                self.service_logger.log_business_event(
                    "跳过视频关联创建",
                    {"reason": "所有关联已存在"}
                )
                return 0

            # 准备批量创建数据
            new_relations = []
            for video_id in new_video_ids:
                relation = UserInboxVideoRelated(
                    uuid=self._generate_uuid(),
                    user_uuid=user_uuid,
                    video_id=video_id,
                    source_type=source_type,
                    is_deleted=False,
                    deleted_at=""
                )
                new_relations.append(relation)

            # 批量创建
            created_count = await self.db_optimizer.batch_create_optimized(
                model=UserInboxVideoRelated,
                records=new_relations,
                batch_size=500
            )

            self.service_logger.log_business_event(
                "视频关联批量创建完成",
                {
                    "total_videos": len(video_ids),
                    "existing_relations": len(existing_video_ids),
                    "new_relations": created_count
                }
            )

            return created_count

        except Exception as e:
            raise DatabaseError(f"处理视频关联失败: {str(e)}")

    async def get_user_source_relations(
        self,
        user_uuid: str,
        source_type: Optional[str] = None
    ) -> List[UserInboxSourceRelated]:
        """
        获取用户的来源关联记录
        
        Args:
            user_uuid: 用户UUID
            source_type: 可选的来源类型过滤
            
        Returns:
            List[UserInboxSourceRelated]: 关联记录列表
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        try:
            query = UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                is_deleted=False
            )
            
            if source_type:
                query = query.filter(source_type=source_type)
            
            return await query.all()
            
        except Exception as e:
            raise DatabaseError(f"查询用户来源关联失败: {str(e)}")

    async def get_user_video_relations(
        self,
        user_uuid: str,
        source_type: Optional[str] = None,
        limit: Optional[int] = None
    ) -> List[UserInboxVideoRelated]:
        """
        获取用户的视频关联记录
        
        Args:
            user_uuid: 用户UUID
            source_type: 可选的来源类型过滤
            limit: 可选的记录数量限制
            
        Returns:
            List[UserInboxVideoRelated]: 关联记录列表
        """
        self.validate_required_params(user_uuid=user_uuid)
        
        try:
            query = UserInboxVideoRelated.filter(
                user_uuid=user_uuid,
                is_deleted=False
            ).order_by("-create_time")
            
            if source_type:
                query = query.filter(source_type=source_type)
                
            if limit:
                query = query.limit(limit)
            
            return await query.all()
            
        except Exception as e:
            raise DatabaseError(f"查询用户视频关联失败: {str(e)}")

    async def remove_source_relation(
        self,
        user_uuid: str,
        source_id: str,
        source_type: str
    ) -> bool:
        """
        软删除来源关联记录
        
        Returns:
            bool: 是否成功删除
        """
        self.validate_required_params(
            user_uuid=user_uuid,
            source_id=source_id,
            source_type=source_type
        )
        
        try:
            # 软删除来源关联
            updated_count = await UserInboxSourceRelated.filter(
                user_uuid=user_uuid,
                source_id=source_id,
                source_type=source_type,
                is_deleted=False
            ).update(
                is_deleted=True,
                deleted_at=str(int(datetime.now().timestamp()))
            )
            
            if updated_count > 0:
                self.service_logger.log_business_event(
                    "来源关联删除成功",
                    {"user_uuid": user_uuid, "source_id": source_id, "source_type": source_type}
                )
                return True
            
            return False
            
        except Exception as e:
            raise DatabaseError(f"删除来源关联失败: {str(e)}")
