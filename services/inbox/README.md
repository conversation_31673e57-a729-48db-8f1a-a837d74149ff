# 收件箱服务模块

用户收件箱关联处理服务，实现用户与来源（作者/关键词）及视频的关联关系管理。

## 📁 模块结构

```
services/inbox/
├── __init__.py                 # 模块导出
├── inbox_service.py           # 主服务入口
├── inbox_relation_service.py  # 关联关系处理
├── video_data_processor.py    # 视频数据处理
├── examples.py               # 使用示例
├── tests.py                  # 单元测试
├── README.md                 # 本文档
└── docs/
    └── user_inbox_relation_flow.md  # 业务流程文档
```

## 🚀 快速开始

### 1. 基础使用

```python
from services.inbox import InboxService

# 初始化服务
inbox_service = InboxService()

# 处理来源视频（新的推荐方式）
from models.douyin import DouyinAweme
from datetime import datetime

# 获取或创建关联对象
user_inbox_source_related = await inbox_service.get_or_create_source_relation(
    user_uuid="user123456789",
    source_id="author_abc123",
    source_type="author"
)

# 使用 DouyinAweme 对象列表
raw_video_list = [
    DouyinAweme(aweme_id="7123456789012345678", create_time=datetime.fromtimestamp(1640995200)),
    DouyinAweme(aweme_id="7123456789012345679", create_time=datetime.strptime("2022-01-01 12:00:00", "%Y-%m-%d %H:%M:%S"))
]

result = await inbox_service.process_source_videos(
    user_inbox_source_related=user_inbox_source_related,
    raw_video_list=raw_video_list,
    enable_deduplication=True
)

print(f"处理结果: {result}")
```

### 1.1. 兼容性方式（保持原有接口）

```python
# 使用兼容性方法（保持原有参数接口）
result = await inbox_service.process_source_videos_by_params(
    user_uuid="user123456789",
    source_id="author_abc123",
    source_type="author",
    raw_video_list=raw_video_list,  # DouyinAweme 对象列表
    enable_deduplication=True
)

print(f"处理结果: {result}")
```

### 2. 批量处理

```python
# 批量处理多个来源
source_data_list = [
    {
        "source_id": "author_001",
        "source_type": "author",
        "videos": [
            {"aweme_id": "7001", "publish_time": 1640995200},
            {"aweme_id": "7002", "publish_time": 1640995300}
        ]
    },
    {
        "source_id": "keyword_001", 
        "source_type": "keyword",
        "videos": [
            {"aweme_id": "7003", "publish_time": 1640995400}
        ]
    }
]

result = await inbox_service.batch_process_multiple_sources(
    user_uuid="user123456789",
    source_data_list=source_data_list,
    batch_size=5
)
```

### 3. 时间过滤

```python
from datetime import datetime, timedelta

# 只处理最近3天的视频
start_time = datetime.now() - timedelta(days=3)

result = await inbox_service.process_source_videos(
    user_uuid="user123456789",
    source_id="author_abc123",
    source_type="author",
    raw_video_list=video_list,
    enable_time_filter=True,
    start_time=start_time
)
```

## 📋 核心服务

### InboxService（主服务）

统一的收件箱关联处理入口，提供完整的业务流程。

**主要方法：**
- `process_source_videos()` - 处理来源视频的完整流程（使用 UserInboxSourceRelated 对象）
- `process_source_videos_by_params()` - 通过参数处理来源视频（兼容性方法）
- `batch_process_multiple_sources()` - 批量处理多个来源
- `get_user_inbox_summary()` - 获取用户收件箱摘要
- `cleanup_user_relations()` - 清理用户关联关系（使用 UserInboxSourceRelated 对象）
- `get_or_create_source_relation()` - 获取或创建用户收件箱来源关联对象

### InboxRelationService（关联处理）

处理用户收件箱关联关系的核心逻辑。

**主要方法：**
- `process_user_inbox_relation()` - 核心关联处理逻辑
- `get_user_source_relations()` - 获取用户来源关联
- `get_user_video_relations()` - 获取用户视频关联
- `remove_source_relation()` - 软删除来源关联

### VideoDataProcessor（数据处理）

处理视频数据的格式化、验证和优化。

**主要方法：**
- `format_video_list()` - 格式化视频列表
- `deduplicate_videos()` - 视频去重
- `filter_videos_by_time_range()` - 时间范围过滤
- `batch_process_videos()` - 视频分批处理

## 🔧 功能特性

### ✅ 数据处理
- **格式化处理**: 自动格式化各种时间格式
- **数据验证**: 验证视频数据和来源数据的有效性
- **去重处理**: 基于视频ID的自动去重
- **分批处理**: 大数据量的分批处理优化

### ✅ 业务逻辑
- **关联管理**: 用户与来源、视频的关联关系管理
- **软删除**: 支持软删除，保证数据安全
- **事务安全**: 数据库操作的事务安全保证
- **批量优化**: 批量数据库操作，提高性能

### ✅ 监控和日志
- **性能监控**: 操作耗时和性能指标监控
- **业务日志**: 详细的业务操作日志记录
- **错误追踪**: 异常和错误的追踪记录
- **统计报告**: 处理结果的统计和摘要

## 📊 数据模型

### UserInboxSourceRelated（来源关联表）
- `user_uuid`: 用户UUID
- `source_id`: 来源ID
- `source_type`: 来源类型（video/author）
- `is_deleted`: 软删除标记
- `deleted_at`: 删除时间

### UserInboxVideoRelated（视频关联表）
- `user_uuid`: 用户UUID
- `video_id`: 视频ID
- `source_type`: 来源类型（author/keyword）
- `is_deleted`: 软删除标记
- `deleted_at`: 删除时间

## 🧪 测试

运行单元测试：

```bash
python -m unittest services.inbox.tests
```

运行示例代码：

```bash
python services/inbox/examples.py
```

## 📖 详细文档

- [业务流程文档](docs/user_inbox_relation_flow.md) - 详细的业务流程说明
- [使用示例](examples.py) - 完整的使用示例代码
- [单元测试](tests.py) - 测试用例和测试方法

## ⚠️ 注意事项

1. **数据库连接**: 使用前请确保数据库连接已正确配置
2. **参数验证**: 所有必要参数都会进行验证，请确保传入有效数据
3. **异常处理**: 服务会抛出相应的异常，请做好异常捕获
4. **性能考虑**: 大数据量处理时建议使用批量处理功能
5. **日志配置**: 建议配置适当的日志级别以便调试和监控

## 🔄 业务流程

1. **来源关联检查**: 检查用户与来源的关联关系是否存在
2. **来源关联创建**: 如不存在则创建新的关联关系
3. **视频数据处理**: 格式化、验证、去重视频数据
4. **视频关联批量处理**: 批量检查和创建视频关联关系
5. **结果统计**: 生成处理结果统计和摘要

详细流程请参考 [业务流程文档](docs/user_inbox_relation_flow.md)。
