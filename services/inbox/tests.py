"""
收件箱服务测试
"""

import unittest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from services.inbox import InboxService, InboxRelationService, VideoDataProcessor
from models.qihaozhushou import UserInboxSourceRelated
from models.douyin import DouyinAweme


class TestVideoDataProcessor(unittest.TestCase):
    """视频数据处理器测试"""

    def setUp(self):
        self.processor = VideoDataProcessor()

    def test_validate_video_data(self):
        """测试视频数据验证"""
        # 有效数据
        valid_data = {"aweme_id": "7123456789012345678"}
        self.assertTrue(self.processor.validate_video_data(valid_data))
        
        # 无效数据 - 缺少aweme_id
        invalid_data = {"title": "测试视频"}
        self.assertFalse(self.processor.validate_video_data(invalid_data))
        
        # 无效数据 - aweme_id为空
        invalid_data2 = {"aweme_id": ""}
        self.assertFalse(self.processor.validate_video_data(invalid_data2))

    def test_format_video_list(self):
        """测试视频列表格式化"""
        raw_videos = [
            {"aweme_id": "7123", "publish_time": 1640995200},
            {"aweme_id": "7124", "publish_time": "2022-01-01 12:00:00"},
            {"aweme_id": "", "title": "无效视频"},  # 应被过滤
        ]
        
        formatted = self.processor.format_video_list(raw_videos)
        
        # 应该只有2个有效视频
        self.assertEqual(len(formatted), 2)
        self.assertEqual(formatted[0]["aweme_id"], "7123")
        self.assertEqual(formatted[1]["aweme_id"], "7124")

    def test_format_publish_time(self):
        """测试发布时间格式化"""
        # 测试时间戳
        timestamp = 1640995200
        formatted = self.processor._format_publish_time(timestamp)
        self.assertIsInstance(formatted, str)
        
        # 测试字符串时间
        time_str = "2022-01-01 12:00:00"
        formatted = self.processor._format_publish_time(time_str)
        self.assertEqual(formatted, time_str)
        
        # 测试datetime对象
        dt = datetime(2022, 1, 1, 12, 0, 0)
        formatted = self.processor._format_publish_time(dt)
        self.assertEqual(formatted, "2022-01-01 12:00:00")
        
        # 测试空值
        formatted = self.processor._format_publish_time(None)
        self.assertEqual(formatted, "")

    def test_extract_video_ids(self):
        """测试视频ID提取"""
        videos = [
            {"aweme_id": "7123"},
            {"aweme_id": "7124"},
            {"title": "无ID视频"}  # 应被忽略
        ]
        
        video_ids = self.processor.extract_video_ids(videos)
        self.assertEqual(video_ids, ["7123", "7124"])

    def test_deduplicate_videos(self):
        """测试视频去重"""
        videos = [
            {"aweme_id": "7123", "title": "视频1"},
            {"aweme_id": "7124", "title": "视频2"},
            {"aweme_id": "7123", "title": "重复视频1"},  # 重复
        ]
        
        deduplicated = self.processor.deduplicate_videos(videos)
        self.assertEqual(len(deduplicated), 2)
        
        # 检查去重后的ID
        ids = [v["aweme_id"] for v in deduplicated]
        self.assertEqual(set(ids), {"7123", "7124"})

    def test_batch_process_videos(self):
        """测试视频分批处理"""
        videos = [{"aweme_id": f"712{i}"} for i in range(5)]
        
        batches = self.processor.batch_process_videos(videos, batch_size=2)
        
        self.assertEqual(len(batches), 3)  # 5个视频，每批2个，共3批
        self.assertEqual(len(batches[0]), 2)
        self.assertEqual(len(batches[1]), 2)
        self.assertEqual(len(batches[2]), 1)

    def test_validate_source_data(self):
        """测试来源数据验证"""
        # 有效数据
        self.assertTrue(self.processor.validate_source_data("source123", "author"))
        self.assertTrue(self.processor.validate_source_data("source123", "video"))
        self.assertTrue(self.processor.validate_source_data("source123", "keyword"))
        
        # 无效数据
        self.assertFalse(self.processor.validate_source_data("", "author"))
        self.assertFalse(self.processor.validate_source_data("source123", ""))
        self.assertFalse(self.processor.validate_source_data("source123", "invalid"))

    def test_create_processing_summary(self):
        """测试处理摘要创建"""
        summary = self.processor.create_processing_summary(100, 80, 5)
        
        self.assertEqual(summary["original_count"], 100)
        self.assertEqual(summary["processed_count"], 80)
        self.assertEqual(summary["error_count"], 5)
        self.assertEqual(summary["success_rate"], 80.0)
        
        # 测试零除法
        summary_zero = self.processor.create_processing_summary(0, 0, 0)
        self.assertEqual(summary_zero["success_rate"], 0)


class TestInboxRelationService(unittest.IsolatedAsyncioTestCase):
    """收件箱关联服务测试"""

    def setUp(self):
        self.service = InboxRelationService()

    def test_generate_uuid(self):
        """测试UUID生成"""
        uuid1 = self.service._generate_uuid()
        uuid2 = self.service._generate_uuid()
        
        # UUID应该是32位字符串
        self.assertEqual(len(uuid1), 32)
        self.assertEqual(len(uuid2), 32)
        
        # 两个UUID应该不同
        self.assertNotEqual(uuid1, uuid2)
        
        # 不应包含连字符
        self.assertNotIn('-', uuid1)
        self.assertNotIn('-', uuid2)

    @patch('models.qihaozhushou.UserInboxSourceRelated.filter')
    async def test_ensure_source_relation_existing(self, mock_filter):
        """测试确保来源关联 - 记录已存在"""
        # 模拟已存在的记录
        mock_relation = MagicMock()
        mock_relation.uuid = "existing_uuid"
        mock_filter.return_value.first = AsyncMock(return_value=mock_relation)
        
        result = await self.service._ensure_source_relation(
            "user123", "source123", "author"
        )
        
        self.assertEqual(result, 0)  # 应该返回0，表示没有创建新记录

    @patch('models.qihaozhushou.UserInboxSourceRelated.filter')
    @patch('models.qihaozhushou.UserInboxSourceRelated.create')
    async def test_ensure_source_relation_new(self, mock_create, mock_filter):
        """测试确保来源关联 - 创建新记录"""
        # 模拟不存在记录
        mock_filter.return_value.first = AsyncMock(return_value=None)
        
        # 模拟创建新记录
        mock_new_relation = MagicMock()
        mock_new_relation.uuid = "new_uuid"
        mock_create.return_value = mock_new_relation
        
        result = await self.service._ensure_source_relation(
            "user123", "source123", "author"
        )
        
        self.assertEqual(result, 1)  # 应该返回1，表示创建了新记录
        mock_create.assert_called_once()


class TestInboxService(unittest.IsolatedAsyncioTestCase):
    """收件箱服务测试"""

    def setUp(self):
        self.service = InboxService()

    @patch.object(InboxRelationService, 'process_user_inbox_relation')
    async def test_process_source_videos_success(self, mock_process):
        """测试处理来源视频 - 成功场景"""
        # 模拟关联服务返回结果
        mock_process.return_value = {
            "source_relations_created": 1,
            "video_relations_created": 2,
            "total_videos_processed": 2
        }

        raw_videos = [
            {"aweme_id": "7123", "publish_time": 1640995200},
            {"aweme_id": "7124", "publish_time": 1640995300}
        ]

        # 创建模拟的 user_inbox_source_related 对象
        from models.qihaozhushou import UserInboxSourceRelated
        user_inbox_source_related = UserInboxSourceRelated(
            user_uuid="user123",
            source_id="source123",
            source_type="author"
        )

        result = await self.service.process_source_videos(
            user_inbox_source_related=user_inbox_source_related,
            raw_video_list=raw_videos
        )
        
        self.assertTrue(result["success"])
        self.assertIn("relation_result", result)
        self.assertIn("processing_summary", result)
        self.assertEqual(result["video_count"]["original"], 2)

    @patch('models.qihaozhushou.UserInboxSourceRelated.filter')
    @patch.object(InboxRelationService, 'process_user_inbox_relation')
    async def test_process_source_videos_by_params_success(self, mock_process, mock_filter):
        """测试通过参数处理来源视频 - 成功场景（兼容性方法）"""
        # 模拟关联服务返回结果
        mock_process.return_value = {
            "source_relations_created": 1,
            "video_relations_created": 2,
            "total_videos_processed": 2
        }

        # 模拟数据库查询返回None（不存在记录）
        mock_filter.return_value.first = AsyncMock(return_value=None)

        raw_videos = [
            {"aweme_id": "7123", "publish_time": 1640995200},
            {"aweme_id": "7124", "publish_time": 1640995300}
        ]

        result = await self.service.process_source_videos_by_params(
            user_uuid="user123",
            source_id="source123",
            source_type="author",
            raw_video_list=raw_videos
        )

        self.assertTrue(result["success"])
        self.assertIn("relation_result", result)
        self.assertIn("processing_summary", result)
        self.assertEqual(result["video_count"]["original"], 2)

    async def test_process_source_videos_validation_error(self):
        """测试处理来源视频 - 参数验证错误"""
        with self.assertRaises(Exception):
            await self.service.process_source_videos(
                user_uuid="",  # 空用户UUID
                source_id="source123",
                source_type="author",
                raw_video_list=[]
            )

    async def test_process_source_videos_invalid_source_type(self):
        """测试处理来源视频 - 无效来源类型"""
        with self.assertRaises(Exception):
            await self.service.process_source_videos(
                user_uuid="user123",
                source_id="source123",
                source_type="invalid_type",
                raw_video_list=[]
            )

    async def test_batch_process_empty_list(self):
        """测试批量处理 - 空列表"""
        result = await self.service.batch_process_multiple_sources(
            user_uuid="user123",
            source_data_list=[]
        )
        
        self.assertTrue(result["success"])
        self.assertEqual(len(result["results"]), 0)

    @patch.object(InboxRelationService, 'get_user_source_relations')
    @patch.object(InboxRelationService, 'get_user_video_relations')
    async def test_get_user_inbox_summary(self, mock_video_relations, mock_source_relations):
        """测试获取用户收件箱摘要"""
        # 模拟来源关联
        mock_source_rel1 = MagicMock()
        mock_source_rel1.source_type = "author"
        mock_source_rel2 = MagicMock()
        mock_source_rel2.source_type = "video"
        mock_source_relations.return_value = [mock_source_rel1, mock_source_rel2]
        
        # 模拟视频关联
        mock_video_rel1 = MagicMock()
        mock_video_rel1.source_type = "author"
        mock_video_relations.return_value = [mock_video_rel1]
        
        summary = await self.service.get_user_inbox_summary("user123")
        
        self.assertEqual(summary["user_uuid"], "user123")
        self.assertEqual(summary["source_relations"]["total"], 2)
        self.assertEqual(summary["video_relations"]["total"], 1)
        self.assertIn("last_updated", summary)


if __name__ == "__main__":
    # 运行测试
    unittest.main()
